import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Badge, Al<PERSON>, Button, Modal } from 'react-bootstrap';
import { useParams, Link, useNavigate } from 'react-router-dom';
import { coursesAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import { ArrowLeft, User, Clock, Calendar, Lock, Play } from 'lucide-react';

const CourseDetail = () => {
  const { id } = useParams();
  const { isAuthenticated } = useAuth();
  const { isDark } = useTheme();
  const navigate = useNavigate();
  const [course, setCourse] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showLoginModal, setShowLoginModal] = useState(false);

  useEffect(() => {
    const fetchCourse = async () => {
      try {
        // Allow public access to course details for preview
        const response = await fetch(`http://localhost:5000/api/courses/${id}`);
        const data = await response.json();
        setCourse(data);
      } catch (err) {
        setError('الدورة غير موجودة أو غير متاحة');
      } finally {
        setLoading(false);
      }
    };

    fetchCourse();
  }, [id]);

  const handleWatchVideo = () => {
    if (!isAuthenticated) {
      setShowLoginModal(true);
    }
    // If authenticated, video will play normally
  };

  const handleLoginRedirect = () => {
    navigate('/login');
  };

  const getLevelBadgeVariant = (level) => {
    switch (level) {
      case 'مبتدئ':
      case 'Beginner':
        return 'success';
      case 'متوسط':
      case 'Intermediate':
        return 'warning';
      case 'متقدم':
      case 'Advanced':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  if (loading) {
    return (
      <Container className={`py-5 ${isDark ? 'text-light' : ''}`} dir="rtl">
        <div className="text-center">
          <div className={`spinner-border ${isDark ? 'text-info' : 'text-primary'}`} role="status">
            <span className="visually-hidden">جاري التحميل...</span>
          </div>
        </div>
      </Container>
    );
  }

  if (error || !course) {
    return (
      <Container className={`py-5 ${isDark ? 'text-light' : ''}`} dir="rtl">
        <Alert variant="danger">
          {error || 'الدورة غير موجودة'}
        </Alert>
        <Button as={Link} to="/courses" variant={isDark ? "info" : "primary"}>
          <ArrowLeft size={16} className="me-1" />
          العودة إلى الدورات
        </Button>
      </Container>
    );
  }

  return (
    <Container className={`py-5 ${isDark ? 'text-light' : ''}`} dir="rtl">
      <Row className="mb-4">
        <Col>
          <Button as={Link} to="/courses" variant={isDark ? "outline-info" : "outline-primary"} className="mb-3">
            <ArrowLeft size={16} className="me-1" />
            العودة إلى الدورات
          </Button>
        </Col>
      </Row>

      <Row>
        <Col lg={8}>
          <Card className={`shadow-sm border-0 mb-4 ${isDark ? 'bg-dark text-light' : 'bg-white'}`}>
            <div className="position-relative">
              {course.video ? (
                <div className="position-relative">
                  <video
                    controls={isAuthenticated}
                    className="w-100"
                    style={{ height: '400px', objectFit: 'cover' }}
                    poster={course.image ? `http://localhost:5000/${course.image}` : "/api/placeholder/800/400"}
                    onClick={handleWatchVideo}
                  >
                    <source src={`http://localhost:5000/${course.video}`} type="video/mp4" />
                    متصفحك لا يدعم تشغيل الفيديو.
                  </video>
                  {!isAuthenticated && (
                    <div
                      className="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
                      style={{ backgroundColor: 'rgba(0,0,0,0.7)', cursor: 'pointer' }}
                      onClick={handleWatchVideo}
                    >
                      <div className="text-center text-white">
                        <Lock size={48} className="mb-3" />
                        <h4>سجل الدخول لمشاهدة الفيديو</h4>
                        <p>انضم إلى آلاف الطلاب واحصل على وصول كامل</p>
                      </div>
                    </div>
                  )}
                </div>
              ) : course.image ? (
                <div className="position-relative">
                  <img
                    src={`http://localhost:5000/${course.image}`}
                    alt={course.title}
                    className="w-100"
                    style={{ height: '400px', objectFit: 'cover' }}
                  />
                  <div
                    className="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
                    style={{ backgroundColor: 'rgba(0,0,0,0.5)', cursor: 'pointer' }}
                    onClick={handleWatchVideo}
                  >
                    <div className="text-center text-white">
                      <Play size={48} className="mb-3" />
                      <h4>ابدأ مشاهدة الدورة</h4>
                      <p>انضم إلى آلاف الطلاب واحصل على وصول كامل</p>
                    </div>
                  </div>
                </div>
              ) : (
                <div
                  className={`${isDark ? 'bg-info' : 'bg-primary'} d-flex align-items-center justify-content-center text-white`}
                  style={{ height: '400px' }}
                >
                  <div className="text-center">
                    <h4>الفيديو قريباً</h4>
                    <p>سيكون فيديو هذه الدورة متاحاً قريباً</p>
                  </div>
                </div>
              )}
            </div>
          </Card>

          <Card className={`shadow-sm border-0 ${isDark ? 'bg-dark text-light' : 'bg-white'}`}>
            <Card.Body>
              <div className="d-flex justify-content-between align-items-start mb-3">
                <div>
                  <h1 className="fw-bold mb-2">{course.title}</h1>
                  <Badge bg={getLevelBadgeVariant(course.level)} className="mb-3">
                    {course.level}
                  </Badge>
                </div>
              </div>

              <div className="row mb-4 text-muted">
                <div className="col-sm-4 d-flex align-items-center mb-2">
                  <User size={16} className="me-2" />
                  <span>{course.instructor}</span>
                </div>
                <div className="col-sm-4 d-flex align-items-center mb-2">
                  <Clock size={16} className="me-2" />
                  <span>{course.duration}</span>
                </div>
                <div className="col-sm-4 d-flex align-items-center mb-2">
                  <Calendar size={16} className="me-2" />
                  <span>{new Date(course.createdAt).toLocaleDateString()}</span>
                </div>
              </div>

              <div className="mb-4">
                <h3 className="fw-bold mb-3">حول هذه الدورة</h3>
                <p className="text-muted lh-lg">{course.description}</p>
              </div>

              {course.tags && course.tags.length > 0 && (
                <div className="mb-4">
                  <h3 className="fw-bold mb-3">العلامات</h3>
                  <div className="d-flex flex-wrap gap-2">
                    {course.tags.map((tag, index) => (
                      <Badge key={index} bg="secondary" className="px-3 py-2">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          <Card className={`shadow-sm border-0 sticky-top ${isDark ? 'bg-dark text-light' : 'bg-white'}`} style={{ top: '100px' }}>
            <Card.Body>
              <h4 className="fw-bold mb-4">معلومات الدورة</h4>
              
              <div className="mb-3">
                <strong>المدرب:</strong>
                <p className="text-muted mb-0">{course.instructor}</p>
              </div>
              
              <div className="mb-3">
                <strong>المدة:</strong>
                <p className="text-muted mb-0">{course.duration}</p>
              </div>
              
              <div className="mb-3">
                <strong>المستوى:</strong>
                <p className="text-muted mb-0">{course.level}</p>
              </div>
              
              <div className="mb-4">
                <strong>تاريخ الإنشاء:</strong>
                <p className="text-muted mb-0">
                  {new Date(course.createdAt).toLocaleDateString('ar-SA')}
                </p>
              </div>

              <Button 
                variant={isAuthenticated ? "success" : (isDark ? "info" : "primary")} 
                size="lg" 
                className="w-100 mb-3"
                onClick={() => {
                  if (isAuthenticated) {
                    navigate(`/courses/${course._id}/learn`);
                  } else {
                    handleWatchVideo();
                  }
                }}
              >
                {isAuthenticated ? (
                  <>
                    <Play size={16} className="me-2" />
                    ابدأ التعلم
                  </>
                ) : (
                  <>
                    <Lock size={16} className="me-2" />
                    سجل الدخول للوصول الكامل
                  </>
                )}
              </Button>
              
              <div className="text-center">
                <small className="text-muted">
                  {isAuthenticated ? 
                    "استمتع بالتعلم مع مجتمعنا" : 
                    "انضم إلى آلاف الطلاب المسجلين بالفعل"
                  }
                </small>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Login Modal */}
      <Modal show={showLoginModal} onHide={() => setShowLoginModal(false)} centered dir="rtl">
        <Modal.Header closeButton className={isDark ? 'bg-dark text-light border-secondary' : ''}>
          <Modal.Title>مطلوب تسجيل الدخول</Modal.Title>
        </Modal.Header>
        <Modal.Body className={isDark ? 'bg-dark text-light' : ''}>
          <div className="text-center">
            <Lock size={48} className={`mb-3 ${isDark ? 'text-info' : 'text-primary'}`} />
            <h5>للوصول إلى محتوى الدورة الكامل</h5>
            <p className="text-muted">
              سجل الدخول أو أنشئ حساباً جديداً للاستمتاع بجميع ميزات المنصة
            </p>
          </div>
        </Modal.Body>
        <Modal.Footer className={isDark ? 'bg-dark border-secondary' : ''}>
          <Button variant="outline-secondary" onClick={() => setShowLoginModal(false)}>
            إلغاء
          </Button>
          <Button variant={isDark ? "info" : "primary"} onClick={handleLoginRedirect}>
            تسجيل الدخول
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default CourseDetail;