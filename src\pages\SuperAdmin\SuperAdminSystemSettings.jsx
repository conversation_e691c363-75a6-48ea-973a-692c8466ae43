import React, { useState, useEffect } from 'react';
import { 
  Card, 
  <PERSON>, 
  Button, 
  Alert, 
  Row, 
  Col,
  Spinner,
  Badge,
  Table,
  Modal
} from 'react-bootstrap';
import { useTheme } from '../../context/ThemeContext';
import { supedAdminAPI } from '../../services/api';
import { 
  Settings, 
  Save, 
  RefreshCw,
  Database,
  Server,
  Shield,
  AlertTriangle,
  CheckCircle,
  Activity,
  HardDrive,
  Wifi,
  Lock,
  Mail,
  Globe
} from 'lucide-react';

const SuperAdminSystemSettings = () => {
  const { isDark } = useTheme();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [systemStatus, setSystemStatus] = useState(null);
  const [showMaintenanceModal, setShowMaintenanceModal] = useState(false);

  const [settings, setSettings] = useState({
    siteName: 'أكاديمية التعلم',
    siteDescription: 'منصة تعليمية متقدمة',
    allowRegistration: true,
    requireEmailVerification: true,
    maintenanceMode: false,
    maxFileSize: 50, // MB
    allowedFileTypes: 'jpg,jpeg,png,pdf,mp4,mp3',
    sessionTimeout: 30, // minutes
    maxLoginAttempts: 5,
    emailNotifications: true,
    smsNotifications: false,
    backupFrequency: 'daily',
    logRetentionDays: 30
  });

  useEffect(() => {
    fetchSystemSettings();
    fetchSystemStatus();
  }, []);

  const fetchSystemSettings = async () => {
    try {
      setLoading(true);
      // Mock data - replace with actual API call
      // const response = await supedAdminAPI.getSystemSettings();
      // setSettings(response.data);
    } catch (err) {
      setError('فشل في تحميل إعدادات النظام');
      console.error('Error fetching settings:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchSystemStatus = async () => {
    try {
      const response = await supedAdminAPI.getSystemStats();
      setSystemStatus(response.data);
    } catch (err) {
      console.error('Error fetching system status:', err);
      // Mock data
      setSystemStatus({
        server: { status: 'online', uptime: '15 يوم', cpu: 45, memory: 62, disk: 78 },
        database: { status: 'connected', connections: 25, size: '2.5 GB' },
        cache: { status: 'active', hitRate: 89, size: '512 MB' },
        storage: { used: '15.2 GB', total: '100 GB', percentage: 15 }
      });
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSave = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      // await supedAdminAPI.updateSystemSettings(settings);
      setSuccess('تم حفظ الإعدادات بنجاح!');
    } catch (err) {
      setError('فشل في حفظ الإعدادات');
      console.error('Error saving settings:', err);
    } finally {
      setSaving(false);
    }
  };

  const toggleMaintenanceMode = async () => {
    try {
      const newMode = !settings.maintenanceMode;
      setSettings(prev => ({ ...prev, maintenanceMode: newMode }));
      // await supedAdminAPI.toggleMaintenanceMode(newMode);
      setSuccess(`تم ${newMode ? 'تفعيل' : 'إلغاء'} وضع الصيانة`);
    } catch (err) {
      setError('فشل في تغيير وضع الصيانة');
      console.error('Error toggling maintenance mode:', err);
    }
    setShowMaintenanceModal(false);
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'online': { variant: 'success', icon: CheckCircle, label: 'متصل' },
      'connected': { variant: 'success', icon: CheckCircle, label: 'متصل' },
      'active': { variant: 'success', icon: CheckCircle, label: 'نشط' },
      'offline': { variant: 'danger', icon: AlertTriangle, label: 'غير متصل' },
      'error': { variant: 'danger', icon: AlertTriangle, label: 'خطأ' }
    };

    const config = statusConfig[status] || statusConfig.offline;
    const IconComponent = config.icon;

    return (
      <Badge bg={config.variant} className="d-flex align-items-center">
        <IconComponent size={12} className="me-1" />
        {config.label}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="text-center py-5">
        <Spinner animation="border" variant="primary" />
        <p className="mt-3">جاري تحميل إعدادات النظام...</p>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h4 className="mb-1">إعدادات النظام</h4>
          <h3 className="mb-1"> هذه الصفحة في الوضع التجريبي</h3>
          <p className="text-muted mb-0">إدارة وتكوين النظام العام</p>
        </div>
        <div className="d-flex gap-2">
          <Button 
            variant={settings.maintenanceMode ? "danger" : "warning"}
            onClick={() => setShowMaintenanceModal(true)}
            className="d-flex align-items-center"
          >
            <Settings size={16} className="me-2" />
            {settings.maintenanceMode ? 'إلغاء الصيانة' : 'وضع الصيانة'}
          </Button>
          <Button 
            variant="outline-primary" 
            onClick={fetchSystemStatus}
            className="d-flex align-items-center"
          >
            <RefreshCw size={16} className="me-2" />
            تحديث الحالة
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          <AlertTriangle size={18} className="me-2" />
          {error}
        </Alert>
      )}

      {success && (
        <Alert variant="success" className="mb-4">
          <CheckCircle size={18} className="me-2" />
          {success}
        </Alert>
      )}

      <Row className="g-4">
        {/* System Status */}
        <Col lg={4}>
          <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
            <Card.Header className="bg-transparent border-0">
              <h6 className="mb-0 d-flex align-items-center">
                <Activity size={18} className="me-2" />
                حالة النظام
              </h6>
            </Card.Header>
            <Card.Body>
              {systemStatus && (
                <>
                  <div className="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                    <div className="d-flex align-items-center">
                      <Server size={20} className="me-2 text-primary" />
                      <div>
                        <div className="fw-bold">الخادم</div>
                        <small className="text-muted">وقت التشغيل: {systemStatus.server.uptime}</small>
                      </div>
                    </div>
                    {getStatusBadge(systemStatus.server.status)}
                  </div>

                  <div className="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                    <div className="d-flex align-items-center">
                      <Database size={20} className="me-2 text-success" />
                      <div>
                        <div className="fw-bold">قاعدة البيانات</div>
                        <small className="text-muted">الحجم: {systemStatus.database.size}</small>
                      </div>
                    </div>
                    {getStatusBadge(systemStatus.database.status)}
                  </div>

                  <div className="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                    <div className="d-flex align-items-center">
                      <HardDrive size={20} className="me-2 text-info" />
                      <div>
                        <div className="fw-bold">التخزين</div>
                        <small className="text-muted">{systemStatus.storage.used} من {systemStatus.storage.total}</small>
                      </div>
                    </div>
                    <div className="text-end">
                      <div className="progress" style={{ width: '60px', height: '8px' }}>
                        <div 
                          className="progress-bar bg-info" 
                          style={{ width: `${systemStatus.storage.percentage}%` }}
                        ></div>
                      </div>
                      <small>{systemStatus.storage.percentage}%</small>
                    </div>
                  </div>

                  <div className="d-flex justify-content-between align-items-center p-3 bg-light rounded">
                    <div className="d-flex align-items-center">
                      <Wifi size={20} className="me-2 text-warning" />
                      <div>
                        <div className="fw-bold">الذاكرة المؤقتة</div>
                        <small className="text-muted">معدل النجاح: {systemStatus.cache.hitRate}%</small>
                      </div>
                    </div>
                    {getStatusBadge(systemStatus.cache.status)}
                  </div>
                </>
              )}
            </Card.Body>
          </Card>

          {/* Performance Metrics */}
          <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
            <Card.Header className="bg-transparent border-0">
              <h6 className="mb-0 d-flex align-items-center">
                <Activity size={18} className="me-2" />
                مقاييس الأداء
              </h6>
            </Card.Header>
            <Card.Body>
              {systemStatus && (
                <>
                  <div className="mb-3">
                    <div className="d-flex justify-content-between mb-1">
                      <small>استخدام المعالج</small>
                      <small>{systemStatus.server.cpu}%</small>
                    </div>
                    <div className="progress" style={{ height: '8px' }}>
                      <div 
                        className="progress-bar bg-primary" 
                        style={{ width: `${systemStatus.server.cpu}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="mb-3">
                    <div className="d-flex justify-content-between mb-1">
                      <small>استخدام الذاكرة</small>
                      <small>{systemStatus.server.memory}%</small>
                    </div>
                    <div className="progress" style={{ height: '8px' }}>
                      <div 
                        className="progress-bar bg-warning" 
                        style={{ width: `${systemStatus.server.memory}%` }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <div className="d-flex justify-content-between mb-1">
                      <small>استخدام القرص</small>
                      <small>{systemStatus.server.disk}%</small>
                    </div>
                    <div className="progress" style={{ height: '8px' }}>
                      <div 
                        className="progress-bar bg-info" 
                        style={{ width: `${systemStatus.server.disk}%` }}
                      ></div>
                    </div>
                  </div>
                </>
              )}
            </Card.Body>
          </Card>
        </Col>

        {/* Settings Form */}
        <Col lg={8}>
          <Form onSubmit={handleSave}>
            {/* General Settings */}
            <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
              <Card.Header className="bg-transparent border-0">
                <h6 className="mb-0 d-flex align-items-center">
                  <Globe size={18} className="me-2" />
                  الإعدادات العامة
                </h6>
              </Card.Header>
              <Card.Body>
                <Row className="g-3">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>اسم الموقع</Form.Label>
                      <Form.Control
                        type="text"
                        name="siteName"
                        value={settings.siteName}
                        onChange={handleInputChange}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>وصف الموقع</Form.Label>
                      <Form.Control
                        type="text"
                        name="siteDescription"
                        value={settings.siteDescription}
                        onChange={handleInputChange}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="d-flex align-items-center pt-4">
                      <Form.Check
                        type="checkbox"
                        name="allowRegistration"
                        checked={settings.allowRegistration}
                        onChange={handleInputChange}
                        label="السماح بالتسجيل الجديد"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="d-flex align-items-center pt-4">
                      <Form.Check
                        type="checkbox"
                        name="requireEmailVerification"
                        checked={settings.requireEmailVerification}
                        onChange={handleInputChange}
                        label="تتطلب تأكيد البريد الإلكتروني"
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Security Settings */}
            <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
              <Card.Header className="bg-transparent border-0">
                <h6 className="mb-0 d-flex align-items-center">
                  <Shield size={18} className="me-2" />
                  إعدادات الأمان
                </h6>
              </Card.Header>
              <Card.Body>
                <Row className="g-3">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>مهلة الجلسة (دقيقة)</Form.Label>
                      <Form.Control
                        type="number"
                        name="sessionTimeout"
                        value={settings.sessionTimeout}
                        onChange={handleInputChange}
                        min="5"
                        max="120"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>الحد الأقصى لمحاولات تسجيل الدخول</Form.Label>
                      <Form.Control
                        type="number"
                        name="maxLoginAttempts"
                        value={settings.maxLoginAttempts}
                        onChange={handleInputChange}
                        min="3"
                        max="10"
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* File Upload Settings */}
            <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
              <Card.Header className="bg-transparent border-0">
                <h6 className="mb-0 d-flex align-items-center">
                  <HardDrive size={18} className="me-2" />
                  إعدادات رفع الملفات
                </h6>
              </Card.Header>
              <Card.Body>
                <Row className="g-3">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>الحد الأقصى لحجم الملف (MB)</Form.Label>
                      <Form.Control
                        type="number"
                        name="maxFileSize"
                        value={settings.maxFileSize}
                        onChange={handleInputChange}
                        min="1"
                        max="500"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>أنواع الملفات المسموحة</Form.Label>
                      <Form.Control
                        type="text"
                        name="allowedFileTypes"
                        value={settings.allowedFileTypes}
                        onChange={handleInputChange}
                        placeholder="jpg,png,pdf,mp4"
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Notification Settings */}
            <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
              <Card.Header className="bg-transparent border-0">
                <h6 className="mb-0 d-flex align-items-center">
                  <Mail size={18} className="me-2" />
                  إعدادات الإشعارات
                </h6>
              </Card.Header>
              <Card.Body>
                <Row className="g-3">
                  <Col md={6}>
                    <Form.Group className="d-flex align-items-center">
                      <Form.Check
                        type="checkbox"
                        name="emailNotifications"
                        checked={settings.emailNotifications}
                        onChange={handleInputChange}
                        label="إشعارات البريد الإلكتروني"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="d-flex align-items-center">
                      <Form.Check
                        type="checkbox"
                        name="smsNotifications"
                        checked={settings.smsNotifications}
                        onChange={handleInputChange}
                        label="إشعارات الرسائل النصية"
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Backup Settings */}
            <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
              <Card.Header className="bg-transparent border-0">
                <h6 className="mb-0 d-flex align-items-center">
                  <Database size={18} className="me-2" />
                  إعدادات النسخ الاحتياطي
                </h6>
              </Card.Header>
              <Card.Body>
                <Row className="g-3">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>تكرار النسخ الاحتياطي</Form.Label>
                      <Form.Select
                        name="backupFrequency"
                        value={settings.backupFrequency}
                        onChange={handleInputChange}
                      >
                        <option value="hourly">كل ساعة</option>
                        <option value="daily">يومياً</option>
                        <option value="weekly">أسبوعياً</option>
                        <option value="monthly">شهرياً</option>
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>مدة الاحتفاظ بالسجلات (يوم)</Form.Label>
                      <Form.Control
                        type="number"
                        name="logRetentionDays"
                        value={settings.logRetentionDays}
                        onChange={handleInputChange}
                        min="7"
                        max="365"
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Save Button */}
            <div className="d-flex justify-content-end">
              <Button 
                type="submit" 
                variant="primary" 
                disabled={saving}
                className="d-flex align-items-center"
              >
                {saving ? (
                  <>
                    <Spinner size="sm" className="me-2" />
                    جاري الحفظ...
                  </>
                ) : (
                  <>
                    <Save size={18} className="me-2" />
                    حفظ الإعدادات
                  </>
                )}
              </Button>
            </div>
          </Form>
        </Col>
      </Row>

      {/* Maintenance Mode Modal */}
      <Modal show={showMaintenanceModal} onHide={() => setShowMaintenanceModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title className="text-warning">
            {settings.maintenanceMode ? 'إلغاء وضع الصيانة' : 'تفعيل وضع الصيانة'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <Settings size={48} className="text-warning mb-3" />
            <p>
              {settings.maintenanceMode 
                ? 'هل أنت متأكد من إلغاء وضع الصيانة؟ سيتمكن المستخدمون من الوصول للموقع مرة أخرى.'
                : 'هل أنت متأكد من تفعيل وضع الصيانة؟ لن يتمكن المستخدمون من الوصول للموقع.'
              }
            </p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowMaintenanceModal(false)}>
            إلغاء
          </Button>
          <Button 
            variant={settings.maintenanceMode ? "success" : "warning"} 
            onClick={toggleMaintenanceMode}
          >
            {settings.maintenanceMode ? 'إلغاء الصيانة' : 'تفعيل الصيانة'}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default SuperAdminSystemSettings;
