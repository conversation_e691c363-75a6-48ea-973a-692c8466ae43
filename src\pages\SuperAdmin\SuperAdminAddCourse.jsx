import React, { useState } from 'react';
import { 
  Card, 
  Form, 
  Button, 
  Alert, 
  Row, 
  Col,
  Spinner,
  Badge
} from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../../context/ThemeContext';
import { coursesAPI } from '../../services/api';
import { 
  BookOpen, 
  Save, 
  ArrowLeft,
  Upload,
  Plus,
  Trash2,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

const SuperAdminAddCourse = () => {
  const { isDark } = useTheme();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [courseData, setCourseData] = useState({
    title: '',
    description: '',
    duration: '',
    level: 'beginner',
    category: '',
    price: 0,
    isActive: true,
    units: [
      {
        title: '',
        description: '',
        lessons: [
          {
            title: '',
            content: '',
            videoUrl: '',
            duration: ''
          }
        ],
        quiz: {
          questions: [
            {
              question: '',
              options: ['', '', '', ''],
              correctAnswer: 0
            }
          ]
        }
      }
    ]
  });

  const [imageFile, setImageFile] = useState(null);
  const [videoFile, setVideoFile] = useState(null);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setCourseData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleFileChange = (e, type) => {
    const file = e.target.files[0];
    if (type === 'image') {
      setImageFile(file);
    } else if (type === 'video') {
      setVideoFile(file);
    }
  };

  const addUnit = () => {
    setCourseData(prev => ({
      ...prev,
      units: [
        ...prev.units,
        {
          title: '',
          description: '',
          lessons: [
            {
              title: '',
              content: '',
              videoUrl: '',
              duration: ''
            }
          ],
          quiz: {
            questions: [
              {
                question: '',
                options: ['', '', '', ''],
                correctAnswer: 0
              }
            ]
          }
        }
      ]
    }));
  };

  const removeUnit = (unitIndex) => {
    setCourseData(prev => ({
      ...prev,
      units: prev.units.filter((_, index) => index !== unitIndex)
    }));
  };

  const updateUnit = (unitIndex, field, value) => {
    setCourseData(prev => ({
      ...prev,
      units: prev.units.map((unit, index) => 
        index === unitIndex ? { ...unit, [field]: value } : unit
      )
    }));
  };

  const addLesson = (unitIndex) => {
    setCourseData(prev => ({
      ...prev,
      units: prev.units.map((unit, index) => 
        index === unitIndex 
          ? {
              ...unit,
              lessons: [
                ...unit.lessons,
                {
                  title: '',
                  content: '',
                  videoUrl: '',
                  duration: ''
                }
              ]
            }
          : unit
      )
    }));
  };

  const removeLesson = (unitIndex, lessonIndex) => {
    setCourseData(prev => ({
      ...prev,
      units: prev.units.map((unit, index) => 
        index === unitIndex 
          ? {
              ...unit,
              lessons: unit.lessons.filter((_, lIndex) => lIndex !== lessonIndex)
            }
          : unit
      )
    }));
  };

  const updateLesson = (unitIndex, lessonIndex, field, value) => {
    setCourseData(prev => ({
      ...prev,
      units: prev.units.map((unit, uIndex) => 
        uIndex === unitIndex 
          ? {
              ...unit,
              lessons: unit.lessons.map((lesson, lIndex) => 
                lIndex === lessonIndex ? { ...lesson, [field]: value } : lesson
              )
            }
          : unit
      )
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const formData = new FormData();
      
      // Add course data
      Object.keys(courseData).forEach(key => {
        if (key === 'units') {
          formData.append(key, JSON.stringify(courseData[key]));
        } else {
          formData.append(key, courseData[key]);
        }
      });

      // Add files
      if (imageFile) {
        formData.append('image', imageFile);
      }
      if (videoFile) {
        formData.append('video', videoFile);
      }

      await coursesAPI.create(formData);
      setSuccess('تم إنشاء الدورة بنجاح!');
      
      setTimeout(() => {
        navigate('/super-admin/courses');
      }, 2000);

    } catch (err) {
      setError('فشل في إنشاء الدورة: ' + (err.response?.data?.message || err.message));
      console.error('Error creating course:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h4 className="mb-1">إضافة دورة جديدة</h4>
          <p className="text-muted mb-0">إنشاء دورة تعليمية جديدة بصلاحيات المدير العام</p>
        </div>
        <Button 
          variant="outline-secondary" 
          onClick={() => navigate('/super-admin/courses')}
          className="d-flex align-items-center"
        >
          <ArrowLeft size={18} className="me-2" />
          العودة للدورات
        </Button>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          <AlertTriangle size={18} className="me-2" />
          {error}
        </Alert>
      )}

      {success && (
        <Alert variant="success" className="mb-4">
          <CheckCircle size={18} className="me-2" />
          {success}
        </Alert>
      )}

      <Form onSubmit={handleSubmit}>
        {/* Basic Information */}
        <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
          <Card.Header className="bg-transparent border-0">
            <h6 className="mb-0 d-flex align-items-center">
              <BookOpen size={18} className="me-2" />
              المعلومات الأساسية
            </h6>
          </Card.Header>
          <Card.Body>
            <Row className="g-3">
              <Col md={6}>
                <Form.Group>
                  <Form.Label>عنوان الدورة *</Form.Label>
                  <Form.Control
                    type="text"
                    name="title"
                    value={courseData.title}
                    onChange={handleInputChange}
                    required
                    placeholder="أدخل عنوان الدورة"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>المدة الزمنية</Form.Label>
                  <Form.Control
                    type="text"
                    name="duration"
                    value={courseData.duration}
                    onChange={handleInputChange}
                    placeholder="مثال: 4 أسابيع"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>المستوى</Form.Label>
                  <Form.Select
                    name="level"
                    value={courseData.level}
                    onChange={handleInputChange}
                  >
                    <option value="beginner">مبتدئ</option>
                    <option value="intermediate">متوسط</option>
                    <option value="advanced">متقدم</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>التصنيف</Form.Label>
                  <Form.Control
                    type="text"
                    name="category"
                    value={courseData.category}
                    onChange={handleInputChange}
                    placeholder="مثال: البرمجة، التصميم"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>السعر</Form.Label>
                  <Form.Control
                    type="number"
                    name="price"
                    value={courseData.price}
                    onChange={handleInputChange}
                    min="0"
                    step="0.01"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="d-flex align-items-center pt-4">
                  <Form.Check
                    type="checkbox"
                    name="isActive"
                    checked={courseData.isActive}
                    onChange={handleInputChange}
                    label="تفعيل الدورة"
                  />
                </Form.Group>
              </Col>
              <Col md={12}>
                <Form.Group>
                  <Form.Label>وصف الدورة *</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={4}
                    name="description"
                    value={courseData.description}
                    onChange={handleInputChange}
                    required
                    placeholder="أدخل وصف مفصل للدورة"
                  />
                </Form.Group>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Media Files */}
        <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
          <Card.Header className="bg-transparent border-0">
            <h6 className="mb-0 d-flex align-items-center">
              <Upload size={18} className="me-2" />
              الملفات والوسائط
            </h6>
          </Card.Header>
          <Card.Body>
            <Row className="g-3">
              <Col md={6}>
                <Form.Group>
                  <Form.Label>صورة الدورة</Form.Label>
                  <Form.Control
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleFileChange(e, 'image')}
                  />
                  <Form.Text className="text-muted">
                    يفضل أن تكون الصورة بحجم 800x400 بكسل
                  </Form.Text>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>فيديو تعريفي (اختياري)</Form.Label>
                  <Form.Control
                    type="file"
                    accept="video/*"
                    onChange={(e) => handleFileChange(e, 'video')}
                  />
                  <Form.Text className="text-muted">
                    فيديو قصير للتعريف بالدورة
                  </Form.Text>
                </Form.Group>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Units and Lessons */}
        <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
          <Card.Header className="bg-transparent border-0 d-flex justify-content-between align-items-center">
            <h6 className="mb-0 d-flex align-items-center">
              <BookOpen size={18} className="me-2" />
              الوحدات والدروس
            </h6>
            <Button
              variant="outline-primary"
              size="sm"
              onClick={addUnit}
              className="d-flex align-items-center"
            >
              <Plus size={16} className="me-1" />
              إضافة وحدة
            </Button>
          </Card.Header>
          <Card.Body>
            {courseData.units.map((unit, unitIndex) => (
              <Card key={unitIndex} className={`mb-3 ${isDark ? 'bg-secondary' : 'bg-light'}`}>
                <Card.Header className="bg-transparent d-flex justify-content-between align-items-center">
                  <h6 className="mb-0">الوحدة {unitIndex + 1}</h6>
                  {courseData.units.length > 1 && (
                    <Button
                      variant="outline-danger"
                      size="sm"
                      onClick={() => removeUnit(unitIndex)}
                    >
                      <Trash2 size={14} />
                    </Button>
                  )}
                </Card.Header>
                <Card.Body>
                  <Row className="g-3 mb-3">
                    <Col md={6}>
                      <Form.Group>
                        <Form.Label>عنوان الوحدة</Form.Label>
                        <Form.Control
                          type="text"
                          value={unit.title}
                          onChange={(e) => updateUnit(unitIndex, 'title', e.target.value)}
                          placeholder="أدخل عنوان الوحدة"
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group>
                        <Form.Label>وصف الوحدة</Form.Label>
                        <Form.Control
                          type="text"
                          value={unit.description}
                          onChange={(e) => updateUnit(unitIndex, 'description', e.target.value)}
                          placeholder="وصف مختصر للوحدة"
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  {/* Lessons */}
                  <div className="mb-3">
                    <div className="d-flex justify-content-between align-items-center mb-2">
                      <h6 className="mb-0">الدروس</h6>
                      <Button
                        variant="outline-success"
                        size="sm"
                        onClick={() => addLesson(unitIndex)}
                        className="d-flex align-items-center"
                      >
                        <Plus size={14} className="me-1" />
                        إضافة درس
                      </Button>
                    </div>

                    {unit.lessons.map((lesson, lessonIndex) => (
                      <Card key={lessonIndex} className={`mb-2 ${isDark ? 'bg-dark' : 'bg-white'} border`}>
                        <Card.Body className="p-3">
                          <div className="d-flex justify-content-between align-items-center mb-2">
                            <h6 className="mb-0">الدرس {lessonIndex + 1}</h6>
                            {unit.lessons.length > 1 && (
                              <Button
                                variant="outline-danger"
                                size="sm"
                                onClick={() => removeLesson(unitIndex, lessonIndex)}
                              >
                                <Trash2 size={12} />
                              </Button>
                            )}
                          </div>
                          <Row className="g-2">
                            <Col md={6}>
                              <Form.Control
                                type="text"
                                value={lesson.title}
                                onChange={(e) => updateLesson(unitIndex, lessonIndex, 'title', e.target.value)}
                                placeholder="عنوان الدرس"
                                size="sm"
                              />
                            </Col>
                            <Col md={6}>
                              <Form.Control
                                type="text"
                                value={lesson.duration}
                                onChange={(e) => updateLesson(unitIndex, lessonIndex, 'duration', e.target.value)}
                                placeholder="المدة (مثال: 15 دقيقة)"
                                size="sm"
                              />
                            </Col>
                            <Col md={12}>
                              <Form.Control
                                as="textarea"
                                rows={2}
                                value={lesson.content}
                                onChange={(e) => updateLesson(unitIndex, lessonIndex, 'content', e.target.value)}
                                placeholder="محتوى الدرس"
                                size="sm"
                              />
                            </Col>
                          </Row>
                        </Card.Body>
                      </Card>
                    ))}
                  </div>
                </Card.Body>
              </Card>
            ))}
          </Card.Body>
        </Card>

        {/* Submit Button */}
        <div className="d-flex justify-content-end gap-3">
          <Button
            variant="secondary"
            onClick={() => navigate('/super-admin/courses')}
            disabled={loading}
          >
            إلغاء
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={loading}
            className="d-flex align-items-center"
          >
            {loading ? (
              <>
                <Spinner size="sm" className="me-2" />
                جاري الحفظ...
              </>
            ) : (
              <>
                <Save size={18} className="me-2" />
                حفظ الدورة
              </>
            )}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default SuperAdminAddCourse;
