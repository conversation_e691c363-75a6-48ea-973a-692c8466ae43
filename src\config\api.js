// API Configuration - حل مشكلة process.env في المتصفح
const getBaseUrl = () => {
  // في بيئة التطوير
  if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    return 'http://localhost:5000';
  }
  
  // في بيئة الإنتاج - محاولة الحصول على المتغير من مصادر مختلفة
  if (typeof window !== 'undefined') {
    // من window object إذا تم تعيينه
    if (window.REACT_APP_API_URL) {
      return window.REACT_APP_API_URL;
    }
    
    // من localStorage إذا تم حفظه
    const savedUrl = localStorage.getItem('API_URL');
    if (savedUrl) {
      return savedUrl;
    }
  }
  
  // محاولة استخدام import.meta.env (Vite)
  if (typeof import !== 'undefined' && import.meta && import.meta.env) {
    return import.meta.env.VITE_API_URL || 'http://localhost:5000';
  }
  
  // fallback للإنتاج
  return 'https://academy-project-production.up.railway.app';
};

export const API_CONFIG = {
  // Base API URL - uses environment variable with fallback
  BASE_URL: getBaseUrl(),
  
  // API endpoints
  ENDPOINTS: {
    API: '/api',
    AUTH: '/api/auth',
    COURSES: '/api/courses',
    USERS: '/api/user',
    ADMIN: '/api/admin',
    SUPER_ADMIN: '/api/super-admin',
    CHATBOT: '/api/chatbot',
    HONOR_BOARD: '/api/honor-board',
    USER_PROGRESS: '/api/user-progress',
    PUBLIC: '/api/public'
  },
  
  // Helper function to get full API URL
  getApiUrl: (endpoint = '') => {
    return `${API_CONFIG.BASE_URL}${endpoint}`;
  },
  
  // Helper function to get full endpoint URL
  getEndpointUrl: (endpointKey) => {
    const endpoint = API_CONFIG.ENDPOINTS[endpointKey];
    return endpoint ? `${API_CONFIG.BASE_URL}${endpoint}` : API_CONFIG.BASE_URL;
  }
};

export default API_CONFIG;
