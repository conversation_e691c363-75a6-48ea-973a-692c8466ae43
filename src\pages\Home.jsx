import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Badge } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { useTheme } from '../context/ThemeContext';
import {
  <PERSON><PERSON><PERSON>, Users, Award, ArrowRight, Play, Star,
  TrendingUp, Zap, Target, Globe, Brain, Code,
  Sparkles, Rocket, Trophy, CheckCircle
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';

const Home = () => {
  const { isDark } = useTheme();
  const { token } = useAuth();
  const [animatedStats, setAnimatedStats] = useState({ students: 0, courses: 0, instructors: 0 });
  const [isVisible, setIsVisible] = useState(false);

  // Animation for statistics
  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 500);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (isVisible) {
      const animateStats = () => {
        const targets = { students: 5000, courses: 150, instructors: 50 };
        const duration = 2000;
        const steps = 60;
        const stepTime = duration / steps;

        let currentStep = 0;
        const timer = setInterval(() => {
          currentStep++;
          const progress = currentStep / steps;

          setAnimatedStats({
            students: Math.floor(targets.students * progress),
            courses: Math.floor(targets.courses * progress),
            instructors: Math.floor(targets.instructors * progress)
          });

          if (currentStep >= steps) {
            clearInterval(timer);
            setAnimatedStats(targets);
          }
        }, stepTime);

        return () => clearInterval(timer);
      };

      animateStats();
    }
  }, [isVisible]);

  const styles = `
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-10px);
      }
    }

    @keyframes pulse {
      0%, 100% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.05);
      }
    }

    @keyframes slideInRight {
      from {
        opacity: 0;
        transform: translateX(50px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    @keyframes slideInLeft {
      from {
        opacity: 0;
        transform: translateX(-50px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    @keyframes sparkle {
      0%, 100% {
        opacity: 1;
        transform: scale(1);
      }
      50% {
        opacity: 0.7;
        transform: scale(1.2);
      }
    }

    .animate-fadeInUp {
      animation: fadeInUp 0.8s ease-out;
    }

    .animate-float {
      animation: float 3s ease-in-out infinite;
    }

    .animate-pulse {
      animation: pulse 2s ease-in-out infinite;
    }

    .animate-slideInRight {
      animation: slideInRight 0.8s ease-out;
    }

    .animate-slideInLeft {
      animation: slideInLeft 0.8s ease-out;
    }

    .animate-sparkle {
      animation: sparkle 1.5s ease-in-out infinite;
    }

    .hero-gradient {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      position: relative;
      overflow: hidden;
    }

    .hero-gradient::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      pointer-events: none;
    }

    .feature-card {
      transition: all 0.3s ease;
      border: none !important;
      background: ${isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(255, 255, 255, 0.9)'};
      backdrop-filter: blur(10px);
    }

    .feature-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .stat-card {
      background: ${isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.2)'};
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
    }

    .stat-card:hover {
      transform: scale(1.05);
      background: ${isDark ? 'rgba(255, 255, 255, 0.15)' : 'rgba(255, 255, 255, 0.3)'};
    }

    .floating-shapes {
      position: absolute;
      width: 100%;
      height: 100%;
      overflow: hidden;
      pointer-events: none;
    }

    .floating-shape {
      position: absolute;
      opacity: 0.1;
      animation: float 6s ease-in-out infinite;
    }

    .floating-shape:nth-child(1) {
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    .floating-shape:nth-child(2) {
      top: 20%;
      right: 10%;
      animation-delay: 2s;
    }

    .floating-shape:nth-child(3) {
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }

    .floating-shape:nth-child(4) {
      bottom: 10%;
      right: 20%;
      animation-delay: 1s;
    }

    .text-purple {
      color: #6f42c1 !important;
    }

    .bg-purple {
      background-color: #6f42c1 !important;
    }

    .hero-section {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
    }

    .section-spacing {
      margin-top: 120px;
      margin-bottom: 120px;
    }

    .testimonial-card {
      transition: all 0.3s ease;
      border: none !important;
      background: ${isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(255, 255, 255, 0.95)'};
      backdrop-filter: blur(10px);
    }

    .testimonial-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .cta-section {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      position: relative;
      overflow: hidden;
    }

    .benefit-item {
      transition: all 0.3s ease;
    }

    .benefit-item:hover {
      transform: scale(1.05);
    }

    @media (max-width: 768px) {
      .display-3 {
        font-size: 2.5rem;
      }

      .display-4 {
        font-size: 2rem;
      }

      .display-5 {
        font-size: 1.8rem;
      }

      .hero-section {
        min-height: auto;
        padding: 60px 0;
      }

      .section-spacing {
        margin-top: 60px;
        margin-bottom: 60px;
      }
    }
  `;

  return (
    <div dir="rtl">
      <style>{styles}</style>

      {/* Hero Section */}
      <div className="hero-section text-white position-relative">
        {/* Floating Shapes */}
        <div className="floating-shapes">
          <div className="floating-shape">
            <Code size={60} />
          </div>
          <div className="floating-shape">
            <Brain size={80} />
          </div>
          <div className="floating-shape">
            <Rocket size={70} />
          </div>
          <div className="floating-shape">
            <Target size={50} />
          </div>
        </div>

        <Container className="h-100 d-flex align-items-center">
          <Row className="align-items-center w-100">
            <Col lg={6} className="animate-slideInLeft">
              <div className="mb-4">
                <Badge bg="warning" className="mb-3 px-3 py-2 animate-sparkle">
                  <Sparkles size={16} className="me-2" />
                  منصة التعلم الرائدة
                </Badge>
              </div>

              <h1 className="display-3 fw-bold mb-4 animate-fadeInUp">
                اتقن مهارات
                <span className="text-warning animate-sparkle"> المستقبل </span>
                مع خبراء الصناعة
              </h1>

              <p className="lead mb-4 animate-fadeInUp" style={{ animationDelay: '0.2s' }}>
                انضم إلى <strong className="text-warning">{animatedStats.students.toLocaleString()}+</strong> طالب
                يتعلمون مهارات التكنولوجيا المتطورة من
                <strong className="text-warning"> {animatedStats.instructors}+ </strong>
                محترف في الصناعة
              </p>

              <div className="d-flex gap-3 mb-4 animate-fadeInUp" style={{ animationDelay: '0.4s' }}>
                <Button
                  as={Link}
                  to="/courses"
                  variant="warning"
                  size="lg"
                  className="px-4 py-3 fw-bold animate-pulse"
                >
                  <Play size={20} className="me-2" />
                  ابدأ التعلم الآن
                </Button>

                {!token && (
                  <Button
                    as={Link}
                    to="/register"
                    variant="outline-light"
                    size="lg"
                    className="px-4 py-3"
                  >
                    <Users size={20} className="me-2" />
                    انضم مجاناً
                  </Button>
                )}
              </div>

              {/* Statistics */}
              <Row className="mt-5 animate-fadeInUp" style={{ animationDelay: '0.6s' }}>
                <Col md={4}>
                  <div className="stat-card rounded-3 p-3 text-center mb-3">
                    <h3 className="fw-bold text-warning mb-1">{animatedStats.students.toLocaleString()}+</h3>
                    <small>طالب نشط</small>
                  </div>
                </Col>
                <Col md={4}>
                  <div className="stat-card rounded-3 p-3 text-center mb-3">
                    <h3 className="fw-bold text-warning mb-1">{animatedStats.courses}+</h3>
                    <small>دورة متخصصة</small>
                  </div>
                </Col>
                <Col md={4}>
                  <div className="stat-card rounded-3 p-3 text-center mb-3">
                    <h3 className="fw-bold text-warning mb-1">{animatedStats.instructors}+</h3>
                    <small>خبير ومدرب</small>
                  </div>
                </Col>
              </Row>
            </Col>

            <Col lg={6} className="text-center animate-slideInRight">
              <div className="position-relative">
                <div className="bg-white bg-opacity-10 rounded-4 p-5 animate-float">
                  <div className="mb-4">
                    <BookOpen size={100} className="text-warning animate-pulse" />
                  </div>
                  <h3 className="fw-bold mb-3">رحلة التعلم تبدأ هنا</h3>
                  <p className="mb-4">
                    محتوى متميز • أدوات تفاعلية • مجتمع داعم
                  </p>

                  <div className="d-flex justify-content-center gap-3 mb-4">
                    <div className="text-center">
                      <Trophy size={24} className="text-warning mb-2" />
                      <small>شهادات معتمدة</small>
                    </div>
                    <div className="text-center">
                      <Globe size={24} className="text-info mb-2" />
                      <small>تعلم من أي مكان</small>
                    </div>
                    <div className="text-center">
                      <Zap size={24} className="text-success mb-2" />
                      <small>تعلم سريع</small>
                    </div>
                  </div>

                  <Button
                    as={Link}
                    to="/courses"
                    variant="outline-light"
                    className="px-4 py-2"
                  >
                    استكشف الدورات
                    <ArrowRight size={16} className="ms-2" />
                  </Button>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      {/* Features Section */}
      <Container className={`section-spacing ${isDark ? 'text-light' : ''}`}>
        <Row className="text-center mb-5 animate-fadeInUp">
          <Col>
            <Badge bg="primary" className="mb-3 px-3 py-2">
              <Star size={16} className="me-2" />
              مميزات استثنائية
            </Badge>
            <h2 className="display-5 fw-bold mb-3">
              لماذا تختار
              <span className="text-primary"> منصتنا؟</span>
            </h2>
            <p className="lead text-muted">
              كل ما تحتاجه لتطوير مسيرتك المهنية ومهاراتك في مكان واحد
            </p>
          </Col>
        </Row>

        <Row className="g-4">
          <Col md={4} className="animate-fadeInUp" style={{ animationDelay: '0.1s' }}>
            <Card className={`feature-card h-100 text-center shadow-lg`}>
              <Card.Body className="p-5">
                <div className="mb-4">
                  <div className="d-inline-flex align-items-center justify-content-center rounded-circle bg-primary bg-opacity-10 p-3 mb-3">
                    <BookOpen size={48} className="text-primary animate-pulse" />
                  </div>
                </div>
                <Card.Title className="h4 fw-bold mb-3">محتوى يقوده خبراء</Card.Title>
                <Card.Text className="text-muted mb-4">
                  تعلم من المحترفين في الصناعة الذين يتمتعون بخبرة عملية
                  وخبرة مثبتة في أحدث التقنيات
                </Card.Text>
                <div className="d-flex justify-content-center gap-2">
                  <CheckCircle size={16} className="text-success" />
                  <small className="text-muted">محتوى محدث باستمرار</small>
                </div>
              </Card.Body>
            </Card>
          </Col>

          <Col md={4} className="animate-fadeInUp" style={{ animationDelay: '0.2s' }}>
            <Card className={`feature-card h-100 text-center shadow-lg`}>
              <Card.Body className="p-5">
                <div className="mb-4">
                  <div className="d-inline-flex align-items-center justify-content-center rounded-circle bg-success bg-opacity-10 p-3 mb-3">
                    <Brain size={48} className="text-success animate-pulse" />
                  </div>
                </div>
                <Card.Title className="h4 fw-bold mb-3">ذكاء اصطناعي متقدم</Card.Title>
                <Card.Text className="text-muted mb-4">
                  تفاعل مع روبوت الدردشة المدعوم بالذكاء الاصطناعي للحصول على
                  مساعدة فورية ودعم تعلم مخصص على مدار الساعة
                </Card.Text>
                <div className="d-flex justify-content-center gap-2">
                  <CheckCircle size={16} className="text-success" />
                  <small className="text-muted">دعم 24/7</small>
                </div>
              </Card.Body>
            </Card>
          </Col>

          <Col md={4} className="animate-fadeInUp" style={{ animationDelay: '0.3s' }}>
            <Card className={`feature-card h-100 text-center shadow-lg`}>
              <Card.Body className="p-5">
                <div className="mb-4">
                  <div className="d-inline-flex align-items-center justify-content-center rounded-circle bg-warning bg-opacity-10 p-3 mb-3">
                    <TrendingUp size={48} className="text-warning animate-pulse" />
                  </div>
                </div>
                <Card.Title className="h4 fw-bold mb-3">نمو مهني مضمون</Card.Title>
                <Card.Text className="text-muted mb-4">
                  اكتسب المهارات التي تحتاجها لتطوير مسيرتك المهنية مع
                  دورات ذات صلة مباشرة بسوق العمل
                </Card.Text>
                <div className="d-flex justify-content-center gap-2">
                  <CheckCircle size={16} className="text-success" />
                  <small className="text-muted">شهادات معتمدة</small>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Additional Features Row */}
        <Row className="g-4 mt-4">
          <Col md={3} className="animate-fadeInUp" style={{ animationDelay: '0.4s' }}>
            <div className="text-center p-4">
              <div className="d-inline-flex align-items-center justify-content-center rounded-circle bg-info bg-opacity-10 p-3 mb-3">
                <Globe size={32} className="text-info" />
              </div>
              <h6 className="fw-bold">تعلم من أي مكان</h6>
              <small className="text-muted">وصول كامل عبر جميع الأجهزة</small>
            </div>
          </Col>

          <Col md={3} className="animate-fadeInUp" style={{ animationDelay: '0.5s' }}>
            <div className="text-center p-4">
              <div className="d-inline-flex align-items-center justify-content-center rounded-circle bg-danger bg-opacity-10 p-3 mb-3">
                <Zap size={32} className="text-danger" />
              </div>
              <h6 className="fw-bold">تعلم سريع وفعال</h6>
              <small className="text-muted">منهجية تعلم مبتكرة</small>
            </div>
          </Col>

          <Col md={3} className="animate-fadeInUp" style={{ animationDelay: '0.6s' }}>
            <div className="text-center p-4">
              <div className="d-inline-flex align-items-center justify-content-center rounded-circle bg-purple bg-opacity-10 p-3 mb-3">
                <Users size={32} className="text-purple" />
              </div>
              <h6 className="fw-bold">مجتمع داعم</h6>
              <small className="text-muted">تفاعل مع آلاف المتعلمين</small>
            </div>
          </Col>

          <Col md={3} className="animate-fadeInUp" style={{ animationDelay: '0.7s' }}>
            <div className="text-center p-4">
              <div className="d-inline-flex align-items-center justify-content-center rounded-circle bg-success bg-opacity-10 p-3 mb-3">
                <Trophy size={32} className="text-success" />
              </div>
              <h6 className="fw-bold">إنجازات وجوائز</h6>
              <small className="text-muted">نظام تحفيزي متقدم</small>
            </div>
          </Col>
        </Row>
      </Container>

      {/* Success Stories Section */}
      <div className={`${isDark ? 'bg-dark' : 'bg-light'} section-spacing py-5`}>
        <Container>
          <Row className="text-center mb-5 animate-fadeInUp">
            <Col>
              <Badge bg="success" className="mb-3 px-3 py-2">
                <Trophy size={16} className="me-2" />
                قصص نجاح
              </Badge>
              <h2 className="display-5 fw-bold mb-3">
                طلابنا يحققون
                <span className="text-success"> النجاح</span>
              </h2>
              <p className="lead text-muted">
                اكتشف كيف غيرت منصتنا حياة آلاف المتعلمين حول العالم
              </p>
            </Col>
          </Row>

          <Row className="g-4">
            <Col md={4} className="animate-fadeInUp" style={{ animationDelay: '0.1s' }}>
              <Card className="testimonial-card h-100 text-center shadow-lg">
                <Card.Body className="p-4">
                  <div className="mb-3">
                    <img
                      src="https://via.placeholder.com/80x80/007bff/ffffff?text=أ.م"
                      alt="Student"
                      className="rounded-circle mb-3"
                      style={{ width: '80px', height: '80px' }}
                    />
                  </div>
                  <blockquote className="mb-3">
                    <p className="text-muted fst-italic">
                      "تعلمت البرمجة من الصفر وحصلت على وظيفة أحلامي في شركة تقنية كبرى"
                    </p>
                  </blockquote>
                  <div>
                    <h6 className="fw-bold mb-1">أحمد محمد</h6>
                    <small className="text-muted">مطور برمجيات</small>
                  </div>
                  <div className="mt-3">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} size={16} className="text-warning" fill="currentColor" />
                    ))}
                  </div>
                </Card.Body>
              </Card>
            </Col>

            <Col md={4} className="animate-fadeInUp" style={{ animationDelay: '0.2s' }}>
              <Card className="testimonial-card h-100 text-center shadow-lg">
                <Card.Body className="p-4">
                  <div className="mb-3">
                    <img
                      src="https://via.placeholder.com/80x80/28a745/ffffff?text=س.ع"
                      alt="Student"
                      className="rounded-circle mb-3"
                      style={{ width: '80px', height: '80px' }}
                    />
                  </div>
                  <blockquote className="mb-3">
                    <p className="text-muted fst-italic">
                      "الدورات التفاعلية والذكاء الاصطناعي ساعدني في فهم المفاهيم المعقدة بسهولة"
                    </p>
                  </blockquote>
                  <div>
                    <h6 className="fw-bold mb-1">سارة علي</h6>
                    <small className="text-muted">مصممة UX/UI</small>
                  </div>
                  <div className="mt-3">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} size={16} className="text-warning" fill="currentColor" />
                    ))}
                  </div>
                </Card.Body>
              </Card>
            </Col>

            <Col md={4} className="animate-fadeInUp" style={{ animationDelay: '0.3s' }}>
              <Card className="testimonial-card h-100 text-center shadow-lg">
                <Card.Body className="p-4">
                  <div className="mb-3">
                    <img
                      src="https://via.placeholder.com/80x80/ffc107/ffffff?text=م.ح"
                      alt="Student"
                      className="rounded-circle mb-3"
                      style={{ width: '80px', height: '80px' }}
                    />
                  </div>
                  <blockquote className="mb-3">
                    <p className="text-muted fst-italic">
                      "بفضل الشهادات المعتمدة تمكنت من الحصول على ترقية في عملي"
                    </p>
                  </blockquote>
                  <div>
                    <h6 className="fw-bold mb-1">محمد حسن</h6>
                    <small className="text-muted">مدير مشاريع تقنية</small>
                  </div>
                  <div className="mt-3">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} size={16} className="text-warning" fill="currentColor" />
                    ))}
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </div>

      {/* CTA Section */}
      <div className="cta-section text-white py-5 position-relative section-spacing">
        <div className="floating-shapes">
          <div className="floating-shape">
            <Rocket size={60} />
          </div>
          <div className="floating-shape">
            <Target size={50} />
          </div>
        </div>

        <Container>
          <Row className="text-center">
            <Col lg={8} className="mx-auto animate-fadeInUp">
              <div className="mb-4">
                <Rocket size={60} className="text-warning animate-float mb-3" />
              </div>

              <h2 className="display-4 fw-bold mb-4">
                مستعد لبدء
                <span className="text-warning"> رحلة التعلم؟</span>
              </h2>

              <p className="lead mb-5">
                انضم إلى <strong className="text-warning">{animatedStats.students.toLocaleString()}+</strong> متعلم
                واتخذ الخطوة التالية في مسيرتك المهنية اليوم
              </p>

              <div className="d-flex justify-content-center gap-3 flex-wrap">
                <Button
                  as={Link}
                  to="/courses"
                  variant="warning"
                  size="lg"
                  className="px-5 py-3 fw-bold animate-pulse"
                >
                  <Play size={20} className="me-2" />
                  استكشف الدورات
                </Button>

                {!token && (
                  <Button
                    as={Link}
                    to="/register"
                    variant="outline-light"
                    size="lg"
                    className="px-5 py-3"
                  >
                    <Users size={20} className="me-2" />
                    سجل مجاناً الآن
                  </Button>
                )}
              </div>

              <div className="mt-5 pt-4 border-top border-light border-opacity-25">
                <Row className="text-center">
                  <Col md={3}>
                    <div className="benefit-item mb-3">
                      <CheckCircle size={24} className="text-success mb-2" />
                      <div>
                        <strong>تسجيل مجاني</strong>
                        <br />
                        <small className="text-light opacity-75">بدون رسوم خفية</small>
                      </div>
                    </div>
                  </Col>
                  <Col md={3}>
                    <div className="benefit-item mb-3">
                      <CheckCircle size={24} className="text-success mb-2" />
                      <div>
                        <strong>شهادات معتمدة</strong>
                        <br />
                        <small className="text-light opacity-75">معترف بها دولياً</small>
                      </div>
                    </div>
                  </Col>
                  <Col md={3}>
                    <div className="benefit-item mb-3">
                      <CheckCircle size={24} className="text-success mb-2" />
                      <div>
                        <strong>دعم مستمر</strong>
                        <br />
                        <small className="text-light opacity-75">24/7 مساعدة</small>
                      </div>
                    </div>
                  </Col>
                  <Col md={3}>
                    <div className="benefit-item mb-3">
                      <CheckCircle size={24} className="text-success mb-2" />
                      <div>
                        <strong>تحديثات مجانية</strong>
                        <br />
                        <small className="text-light opacity-75">محتوى جديد دائماً</small>
                      </div>
                    </div>
                  </Col>
                </Row>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </div>
  );
};

export default Home;