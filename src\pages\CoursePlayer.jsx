import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Button, Alert, Modal, Form, ProgressBar, Badge } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import { 
  Play, 
  BookOpen, 
  CheckCircle, 
  Lock, 
  ArrowLeft, 
  ArrowRight,
  Award,
  Clock
} from 'lucide-react';

const CoursePlayer = () => {
  const { id } = useParams();
  const { user } = useAuth();
  const { isDark } = useTheme();
  const navigate = useNavigate();
  
  const [course, setCourse] = useState(null);
  const [progress, setProgress] = useState(null);
  const [currentUnit, setCurrentUnit] = useState(0);
  const [currentLesson, setCurrentLesson] = useState(0);
  const [showQuiz, setShowQuiz] = useState(false);
  const [quizAnswers, setQuizAnswers] = useState([]);
  const [quizResult, setQuizResult] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchCourseData();
  }, [id]);

  const fetchCourseData = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/courses/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      const data = await response.json();
      
      setCourse(data.course);
      setProgress(data.progress);
      
      if (data.progress) {
        setCurrentUnit(data.progress.currentUnitIndex);
      }
    } catch (err) {
      setError('فشل في تحميل الدورة');
    } finally {
      setLoading(false);
    }
  };

  const markLessonComplete = async (unitIndex, lessonIndex) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/progress/lesson/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          courseId: id,
          unitIndex,
          lessonIndex
        })
      });

      const updatedProgress = await response.json();
      setProgress(updatedProgress);
    } catch (err) {
      console.error('Error marking lesson complete:', err);
    }
  };

  const submitQuiz = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/progress/quiz/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          courseId: id,
          unitIndex: currentUnit,
          answers: quizAnswers
        })
      });

      const result = await response.json();
      setQuizResult(result);
      setProgress(result.progress);
      
      if (result.passed) {
        setTimeout(() => {
          setShowQuiz(false);
          setQuizResult(null);
          setQuizAnswers([]);
        }, 3000);
      }
    } catch (err) {
      setError('فشل في إرسال الاختبار');
    }
  };

  const isLessonCompleted = (unitIndex, lessonIndex) => {
    if (!progress) return false;
    return progress.completedLessons.some(
      lesson => lesson.unitIndex === unitIndex && lesson.lessonIndex === lessonIndex
    );
  };

  const isQuizPassed = (unitIndex) => {
    if (!progress) return false;
    const quiz = progress.passedQuizzes.find(q => q.unitIndex === unitIndex);
    return quiz && quiz.score >= (course?.units[unitIndex]?.quiz?.passingScore || 70);
  };

  const canAccessUnit = (unitIndex) => {
    if (unitIndex === 0) return true;
    return isQuizPassed(unitIndex - 1);
  };

  if (loading) {
    return (
      <Container className={`py-5 ${isDark ? 'text-light' : ''}`} dir="rtl">
        <div className="text-center">
          <div className={`spinner-border ${isDark ? 'text-info' : 'text-primary'}`} role="status">
            <span className="visually-hidden">جاري التحميل...</span>
          </div>
        </div>
      </Container>
    );
  }

  if (error || !course) {
    return (
      <Container className={`py-5 ${isDark ? 'text-light' : ''}`} dir="rtl">
        <Alert variant="danger">{error || 'الدورة غير موجودة'}</Alert>
      </Container>
    );
  }

  const currentUnitData = course.units[currentUnit];
  const currentLessonData = currentUnitData?.lessons[currentLesson];

  return (
    <Container fluid className={`py-4 ${isDark ? 'text-light' : ''}`} dir="rtl">
      <Row>
        {/* Sidebar */}
        <Col md={3} className="mb-4">
          <Card className={`shadow-sm border-0 ${isDark ? 'bg-dark text-light' : 'bg-white'}`}>
            <Card.Header className={`${isDark ? 'bg-secondary' : 'bg-light'} border-0`}>
              <h5 className="fw-bold mb-0">{course.title}</h5>
              {progress && (
                <ProgressBar 
                  now={progress.progressPercentage} 
                  label={`${progress.progressPercentage}%`}
                  className="mt-2"
                  variant={isDark ? "info" : "primary"}
                />
              )}
            </Card.Header>
            <Card.Body className="p-0">
              {course.units.map((unit, unitIndex) => (
                <div key={unitIndex} className="border-bottom">
                  <div 
                    className={`p-3 ${currentUnit === unitIndex ? (isDark ? 'bg-info' : 'bg-primary') + ' text-white' : ''}`}
                    style={{ cursor: canAccessUnit(unitIndex) ? 'pointer' : 'not-allowed' }}
                    onClick={() => canAccessUnit(unitIndex) && setCurrentUnit(unitIndex)}
                  >
                    <div className="d-flex align-items-center justify-content-between">
                      <div className="d-flex align-items-center">
                        {canAccessUnit(unitIndex) ? (
                          isQuizPassed(unitIndex) ? (
                            <CheckCircle size={16} className="me-2 text-success" />
                          ) : (
                            <BookOpen size={16} className="me-2" />
                          )
                        ) : (
                          <Lock size={16} className="me-2 text-muted" />
                        )}
                        <span className="fw-semibold">{unit.title}</span>
                      </div>
                      <Badge bg={isQuizPassed(unitIndex) ? 'success' : 'secondary'}>
                        {unit.lessons.length}
                      </Badge>
                    </div>
                  </div>
                  
                  {currentUnit === unitIndex && (
                    <div className="ps-4">
                      {unit.lessons.map((lesson, lessonIndex) => (
                        <div
                          key={lessonIndex}
                          className={`p-2 border-bottom cursor-pointer ${currentLesson === lessonIndex ? (isDark ? 'bg-secondary' : 'bg-light') : ''}`}
                          onClick={() => setCurrentLesson(lessonIndex)}
                        >
                          <div className="d-flex align-items-center">
                            {isLessonCompleted(unitIndex, lessonIndex) ? (
                              <CheckCircle size={14} className="me-2 text-success" />
                            ) : (
                              <Play size={14} className="me-2 text-muted" />
                            )}
                            <small>{lesson.title}</small>
                          </div>
                        </div>
                      ))}
                      
                      <div className="p-2">
                        <Button
                          variant={isQuizPassed(unitIndex) ? "success" : "warning"}
                          size="sm"
                          className="w-100"
                          onClick={() => setShowQuiz(true)}
                          disabled={!canAccessUnit(unitIndex)}
                        >
                          <Award size={14} className="me-1" />
                          {isQuizPassed(unitIndex) ? 'اختبار مجتاز' : 'اختبار الوحدة'}
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </Card.Body>
          </Card>
        </Col>

        {/* Main Content */}
        <Col md={9}>
          <Card className={`shadow-sm border-0 ${isDark ? 'bg-dark text-light' : 'bg-white'}`}>
            <Card.Header className={`${isDark ? 'bg-secondary' : 'bg-light'} border-0`}>
              <div className="d-flex justify-content-between align-items-center">
                <h4 className="fw-bold mb-0">{currentLessonData?.title}</h4>
                <div className="d-flex gap-2">
                  <Button
                    variant="outline-secondary"
                    size="sm"
                    onClick={() => navigate(`/courses/${id}`)}
                  >
                    <ArrowLeft size={16} className="me-1" />
                    العودة
                  </Button>
                </div>
              </div>
            </Card.Header>
            
            <Card.Body>
              {currentLessonData && (
                <div>
                  {currentLessonData.type === 'video' && (
                    <div className="mb-4">
                      <div className="ratio ratio-16x9">
                        {currentLessonData.videoUrl.includes('youtube.com') || currentLessonData.videoUrl.includes('youtu.be') ? (
                          <iframe
                            src={currentLessonData.videoUrl.replace('watch?v=', 'embed/')}
                            title={currentLessonData.title}
                            allowFullScreen
                          ></iframe>
                        ) : (
                          <video controls className="w-100">
                            <source src={currentLessonData.videoUrl} type="video/mp4" />
                            متصفحك لا يدعم تشغيل الفيديو.
                          </video>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {currentLessonData.type === 'reading' && (
                    <div className="mb-4">
                      <div 
                        className="lesson-content"
                        dangerouslySetInnerHTML={{ __html: currentLessonData.textContent }}
                      />
                    </div>
                  )}
                  
                  <div className="d-flex justify-content-between align-items-center">
                    <div className="d-flex align-items-center">
                      <Clock size={16} className="me-2 text-muted" />
                      <small className="text-muted">{currentLessonData.duration}</small>
                    </div>
                    
                    <Button
                      variant={isLessonCompleted(currentUnit, currentLesson) ? "success" : "primary"}
                      onClick={() => markLessonComplete(currentUnit, currentLesson)}
                      disabled={isLessonCompleted(currentUnit, currentLesson)}
                    >
                      {isLessonCompleted(currentUnit, currentLesson) ? (
                        <>
                          <CheckCircle size={16} className="me-1" />
                          مكتمل
                        </>
                      ) : (
                        'تم الانتهاء'
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Quiz Modal */}
      <Modal show={showQuiz} onHide={() => setShowQuiz(false)} size="lg" centered dir="rtl">
        <Modal.Header closeButton className={isDark ? 'bg-dark text-light border-secondary' : ''}>
          <Modal.Title>اختبار الوحدة: {currentUnitData?.title}</Modal.Title>
        </Modal.Header>
        <Modal.Body className={isDark ? 'bg-dark text-light' : ''}>
          {quizResult ? (
            <div className="text-center">
              <div className={`mb-3 ${quizResult.passed ? 'text-success' : 'text-danger'}`}>
                <Award size={48} />
              </div>
              <h4 className={quizResult.passed ? 'text-success' : 'text-danger'}>
                {quizResult.passed ? 'مبروك! لقد نجحت' : 'لم تنجح في الاختبار'}
              </h4>
              <p>
                النتيجة: {quizResult.score}% ({quizResult.correctAnswers}/{quizResult.totalQuestions})
              </p>
              {!quizResult.passed && (
                <p className="text-muted">
                  تحتاج إلى {currentUnitData?.quiz?.passingScore || 70}% للنجاح. يمكنك إعادة المحاولة.
                </p>
              )}
            </div>
          ) : (
            <div>
              {currentUnitData?.quiz?.questions.map((question, index) => (
                <div key={index} className="mb-4">
                  <h6 className="fw-bold mb-3">
                    السؤال {index + 1}: {question.question}
                  </h6>
                  {question.options.map((option, optionIndex) => (
                    <Form.Check
                      key={optionIndex}
                      type="radio"
                      name={`question-${index}`}
                      label={option}
                      value={optionIndex}
                      onChange={(e) => {
                        const newAnswers = [...quizAnswers];
                        newAnswers[index] = parseInt(e.target.value);
                        setQuizAnswers(newAnswers);
                      }}
                      className="mb-2"
                    />
                  ))}
                </div>
              ))}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer className={isDark ? 'bg-dark border-secondary' : ''}>
          {!quizResult ? (
            <>
              <Button variant="secondary" onClick={() => setShowQuiz(false)}>
                إلغاء
              </Button>
              <Button 
                variant="primary" 
                onClick={submitQuiz}
                disabled={quizAnswers.length !== currentUnitData?.quiz?.questions.length}
              >
                إرسال الإجابات
              </Button>
            </>
          ) : (
            <Button 
              variant={quizResult.passed ? "success" : "warning"} 
              onClick={() => {
                setShowQuiz(false);
                setQuizResult(null);
                setQuizAnswers([]);
              }}
            >
              {quizResult.passed ? 'متابعة' : 'إعادة المحاولة'}
            </Button>
          )}
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default CoursePlayer;