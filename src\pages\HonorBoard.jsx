import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, Alert } from 'react-bootstrap';
import { useTheme } from '../context/ThemeContext';
import { Trophy, Medal, Award, Star } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import StudentHonnorBoard from '../components/StudentHonnorBoard.jsx'; // Assuming this is the component for the temporary table


const API_URI = 'http://localhost:5000'|| 'https://academy-project-backend.onrender.com' ||process.env.REACT_APP_API_URL 

const HonorBoard = () => {
  const { user } = useAuth();
  const { isDark } = useTheme();
  const [honorBoard, setHonorBoard] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [users, setUsers] = useState([]); // For the temporary table of all students

  useEffect(() => {
    fetchHonorBoard();
  }, []);

  const fetchHonorBoard = async () => {
    try {
      const response = await fetch(`${API_URI}/api/honor-board`);
      const data = await response.json();
      setHonorBoard(data);
    } catch (err) {
      setError('فشل في تحميل لائحة الشرف');
    } finally {
      setLoading(false);
    }
  };

  const getRankIcon = (rank) => {
    switch (rank) {
      case 1: return <Trophy size={24} className="text-warning" />;
      case 2: return <Medal size={24} className="text-secondary" />;
      case 3: return <Award size={24} className="text-warning" />;
      default: return <Star size={20} className="text-muted" />;
    }
  };

  const getRankBadge = (rank) => {
    if (rank <= 3) {
      const variants = ['warning', 'secondary', 'warning'];
      return <Badge bg={variants[rank - 1]}>#{rank}</Badge>;
    }
    return <Badge bg="light" text="dark">#{rank}</Badge>;
  };

  if (loading) {
    return (
      <Container className={`py-5 ${isDark ? 'text-light' : ''}`} dir="rtl">
        <div className="text-center">
          <div className={`spinner-border ${isDark ? 'text-info' : 'text-primary'}`} role="status">
            <span className="visually-hidden">جاري التحميل...</span>
          </div>
        </div>
      </Container>
    );
  }

  return (
    <Container className={`py-5 ${isDark ? 'text-light' : ''}`} dir="rtl">
      <Row className="mb-5">
        <Col>
          <div className="text-center">
            <Trophy size={64} className={`${isDark ? 'text-info' : 'text-primary'} mb-3`} />
            <h1 className="fw-bold mb-3">لائحة الشرف</h1>
            <p className="text-muted mb-4">
              تكريم الطلاب المتميزين الذين أكملوا الدورات بنجاح
            </p>
          </div>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" className="mb-4">
          {error}
        </Alert>
      )}

      {/* Top 3 Students */}
      {honorBoard.length > 0 && (
        <Row className="mb-5">
          <Col>
            <h3 className="fw-bold mb-4 text-center">المراكز الأولى</h3>
            <Row className="justify-content-center">
              {honorBoard.slice(0, 3).map((student, index) => (
                <Col key={student._id} md={4} className="mb-4">
                  <Card className={`text-center shadow-sm border-0 h-100 ${isDark ? 'bg-dark text-light' : 'bg-white'}`}>
                    <Card.Body className="p-4">
                      <div className="mb-3">
                        {getRankIcon(student.rank)}
                      </div>
                      <h5 className="fw-bold mb-2">{student.userId.name}</h5>
                      <div className="mb-3">
                        {getRankBadge(student.rank)}
                      </div>
                      <div className="mb-2">
                        <span className={`h4 fw-bold ${isDark ? 'text-info' : 'text-primary'}`}>
                          {student.totalPoints}
                        </span>
                        <small className="text-muted d-block">نقطة</small>
                      </div>
                      <div className="text-muted">
                        <small>{student.completedCourses.length} دورة مكتملة</small>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              ))}
            </Row>
          </Col>
        </Row>
      )}

      {/* Full Honor Board Table */}
      <Row>
        <Col>
          <Card className={`shadow-sm border-0 ${isDark ? 'bg-dark text-light' : 'bg-white'}`}>
            <Card.Header className={`${isDark ? 'bg-secondary' : 'bg-light'} border-0`}>
              <h4 className="fw-bold mb-0">جميع الطلاب المتميزين</h4>
            </Card.Header>
            <Card.Body className="p-0">
              {honorBoard.length > 0 ? (
                <Table responsive hover className={`mb-0 ${isDark ? 'table-dark' : ''}`}>
                  <thead className={isDark ? 'table-secondary' : 'table-light'}>
                    <tr>
                      <th>المرتبة</th>
                      <th>الطالب</th>
                      <th>النقاط الإجمالية</th>
                      <th>الدورات المكتملة</th>
                      <th>آخر إنجاز</th>
                    </tr>
                  </thead>
                  <tbody>
                    {honorBoard.map((student) => (
                      <tr key={student._id}>
                        <td>
                          <div className="d-flex align-items-center">
                            {getRankIcon(student.rank)}
                            <span className="ms-2">{getRankBadge(student.rank)}</span>
                          </div>
                        </td>
                        <td>
                          <div>
                            <div className="fw-semibold">{student.userId.name}</div>
                            <small className="text-muted">{student.userId.email}</small>
                          </div>
                        </td>
                        <td>
                          <span className={`fw-bold ${isDark ? 'text-info' : 'text-primary'}`}>
                            {student.totalPoints}
                          </span>
                        </td>
                        <td>
                          <Badge bg="success" className="me-1">
                            {student.completedCourses.length}
                          </Badge>
                          <small className="text-muted">دورة</small>
                        </td>
                        <td>
                          {student.completedCourses.length > 0 && (
                            <small className="text-muted">
                              {new Date(
                                Math.max(...student.completedCourses.map(c => new Date(c.completedAt)))
                              ).toLocaleDateString('ar-SA')}
                            </small>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              ) : (
                <div className="text-center py-5">
                  <Trophy size={48} className="text-muted mb-3" />
                  <h5 className="text-muted">لا توجد إنجازات بعد</h5>
                  <p className="text-muted">كن أول من يكمل دورة ويظهر في لائحة الشرف!</p>
                </div>
              )}
            </Card.Body>
          </Card>
          {/* only temperary */}
          <StudentHonnorBoard />

        

        </Col>
      </Row>
    </Container>
  );
};

export default HonorBoard;