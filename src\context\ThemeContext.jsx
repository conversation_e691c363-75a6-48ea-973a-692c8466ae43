import React, { createContext, useState, useContext, useEffect } from 'react';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [isDark, setIsDark] = useState(() => {
    const saved = localStorage.getItem('theme');
    return saved ? saved === 'dark' : false;
  });

 useEffect(() => {
  localStorage.setItem('theme', isDark ? 'dark' : 'light');
  document.body.setAttribute('data-bs-theme', isDark ? 'dark' : 'light');
  document.body.className = isDark ? 'bg-dark text-light' : 'bg-light text-dark';
  document.documentElement.style.transition = 'background-color 0.3s, color 0.3s';
}, [isDark]);


  const toggleTheme = () => {
    setIsDark(!isDark);
  };

  const value = {
    isDark,
    toggleTheme,
    theme: isDark ? 'dark' : 'light'
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};