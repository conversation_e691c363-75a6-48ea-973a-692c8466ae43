import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ge,
  <PERSON>,
  <PERSON><PERSON>,
  Spinner,
  InputGroup,
  Form,
  Row,
  Col
} from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { useTheme } from '../../context/ThemeContext';
import { coursesAPI } from '../../services/api';
import { API_CONFIG } from '../../config/api';
import {
  BookOpen,
  Edit,
  Trash2,
  Search,
  Plus,
  Eye,
  Users,
  Clock,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

const SuperAdminCourses = () => {
  const { isDark } = useTheme();
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const response = await coursesAPI.getAll();
      setCourses(response.data);
    } catch (err) {
      setError('فشل في تحميل الدورات');
      console.error('Error fetching courses:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCourse = async () => {
    try {
      await coursesAPI.delete(selectedCourse._id);
      setCourses(courses.filter(course => course._id !== selectedCourse._id));
      setShowDeleteModal(false);
      setSelectedCourse(null);
    } catch (err) {
      setError('فشل في حذف الدورة');
      console.error('Error deleting course:', err);
    }
  };

  const toggleCourseStatus = async (courseId, currentStatus) => {
    try {
      await coursesAPI.update(courseId, { isActive: !currentStatus });
      setCourses(courses.map(course => 
        course._id === courseId 
          ? { ...course, isActive: !currentStatus }
          : course
      ));
    } catch (err) {
      setError('فشل في تحديث حالة الدورة');
      console.error('Error updating course status:', err);
    }
  };

  const getStatusBadge = (isActive) => {
    return isActive ? (
      <Badge bg="success" className="d-flex align-items-center">
        <CheckCircle size={12} className="me-1" />
        نشطة
      </Badge>
    ) : (
      <Badge bg="secondary" className="d-flex align-items-center">
        <AlertTriangle size={12} className="me-1" />
        غير نشطة
      </Badge>
    );
  };

  const filteredCourses = courses.filter(course =>
    course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    course.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="text-center py-5">
        <Spinner animation="border" variant="primary" />
        <p className="mt-3">جاري تحميل الدورات...</p>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h4 className="mb-1">إدارة الدورات</h4>
          <p className="text-muted mb-0">التحكم الكامل في الدورات التعليمية</p>
        </div>
        <Button 
          as={Link} 
          to="/super-admin/add-course" 
          variant="primary" 
          className="d-flex align-items-center"
        >
          <Plus size={18} className="me-2" />
          إضافة دورة جديدة
        </Button>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          <AlertTriangle size={18} className="me-2" />
          {error}
        </Alert>
      )}

      {/* Search */}
      <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
        <Card.Body>
          <Row className="g-3">
            <Col md={6}>
              <InputGroup>
                <InputGroup.Text>
                  <Search size={16} />
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="البحث في الدورات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </Col>
            <Col md={6}>
              <div className="text-muted small d-flex align-items-center justify-content-end">
                إجمالي الدورات: {filteredCourses.length}
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Courses Grid */}
      <Row className="g-4">
        {filteredCourses.map((course) => (
          <Col md={6} lg={4} key={course._id}>
            <Card className={`h-100 ${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
              <div className="position-relative">
                <Card.Img
                  variant="top"
                  src={`${API_CONFIG.BASE_URL}/${course.image}`}
                  style={{ height: '200px', objectFit: 'cover' }}
                />
                <div className="position-absolute top-0 end-0 m-2">
                  {getStatusBadge(course.isActive)}
                </div>
              </div>
              
              <Card.Body className="d-flex flex-column">
                <div className="flex-grow-1">
                  <Card.Title className="h6 mb-2">{course.title}</Card.Title>
                  <Card.Text className="text-muted small mb-3">
                    {course.description.length > 100 
                      ? `${course.description.substring(0, 100)}...`
                      : course.description
                    }
                  </Card.Text>
                  
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <div className="d-flex align-items-center text-muted small">
                      <Users size={14} className="me-1" />
                      {course.enrollmentCount || 0} طالب
                    </div>
                    <div className="d-flex align-items-center text-muted small">
                      <Clock size={14} className="me-1" />
                      {course.duration || 'غير محدد'}
                    </div>
                  </div>
                </div>

                <div className="d-flex gap-1">
                  <Button
                    as={Link}
                    to={`/super-admin/course/${course._id}`}
                    variant="outline-info"
                    size="sm"
                    title="عرض التفاصيل"
                  >
                    <Eye size={14} />
                  </Button>
                  <Button
                    as={Link}
                    to={`/super-admin/edit-course/${course._id}`}
                    variant="outline-warning"
                    size="sm"
                    title="تعديل"
                  >
                    <Edit size={14} />
                  </Button>
                  <Button
                    variant={course.isActive ? "outline-secondary" : "outline-success"}
                    size="sm"
                    onClick={() => toggleCourseStatus(course._id, course.isActive)}
                    title={course.isActive ? 'إيقاف' : 'تفعيل'}
                  >
                    {course.isActive ? 'إيقاف' : 'تفعيل'}
                  </Button>
                  <Button
                    variant="outline-danger"
                    size="sm"
                    onClick={() => {
                      setSelectedCourse(course);
                      setShowDeleteModal(true);
                    }}
                    title="حذف"
                  >
                    <Trash2 size={14} />
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>

      {filteredCourses.length === 0 && !loading && (
        <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
          <Card.Body className="text-center py-5">
            <BookOpen size={48} className="text-muted mb-3" />
            <h5>لا توجد دورات</h5>
            <p className="text-muted">لم يتم العثور على دورات تطابق البحث</p>
            <Button as={Link} to="/super-admin/add-course" variant="primary">
              إضافة دورة جديدة
            </Button>
          </Card.Body>
        </Card>
      )}

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title className="text-danger">تأكيد الحذف</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <AlertTriangle size={48} className="text-danger mb-3" />
            <p>هل أنت متأكد من حذف الدورة: <strong>{selectedCourse?.title}</strong>؟</p>
            <p className="text-muted">سيتم حذف جميع البيانات المرتبطة بهذه الدورة نهائياً.</p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            إلغاء
          </Button>
          <Button variant="danger" onClick={handleDeleteCourse}>
            حذف الدورة
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default SuperAdminCourses;
