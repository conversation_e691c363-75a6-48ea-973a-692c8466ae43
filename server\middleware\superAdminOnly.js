// Middleware للتحقق من صلاحيات المدير العام فقط
export const superAdminOnly = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ 
      message: 'Authentication required',
      code: 'AUTH_REQUIRED'
    });
  }

  if (req.user.role !== 'super-admin') {
    return res.status(403).json({ 
      message: 'Access denied. Super admin rights required.',
      requiredRole: 'super-admin',
      currentRole: req.user.role,
      code: 'INSUFFICIENT_PERMISSIONS'
    });
  }

  next();
};

// Middleware للتحقق من صلاحيات المدير العام مع إمكانية السماح للمدير العادي بالقراءة فقط
export const superAdminOrReadOnly = (allowedMethods = ['GET']) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    // السماح للمدير العام بكل شيء
    if (req.user.role === 'super-admin') {
      return next();
    }

    // السماح للمدير العادي بالعمليات المحددة فقط
    if (req.user.role === 'admin' && allowedMethods.includes(req.method)) {
      return next();
    }

    return res.status(403).json({ 
      message: `Access denied. ${req.method} operation requires super admin rights.`,
      requiredRole: 'super-admin',
      currentRole: req.user.role,
      allowedMethods: req.user.role === 'admin' ? allowedMethods : [],
      code: 'INSUFFICIENT_PERMISSIONS'
    });
  };
};

// Middleware لتسجيل العمليات الحساسة
export const logSensitiveAction = (action) => {
  return (req, res, next) => {
    const timestamp = new Date().toISOString();
    const userInfo = req.user ? `${req.user.email} (${req.user.role})` : 'Unknown';
    const ip = req.ip || req.connection.remoteAddress;
    
    console.log(`[SENSITIVE ACTION] ${timestamp} - ${action} - User: ${userInfo} - IP: ${ip} - Path: ${req.path}`);
    
    // في تطبيق حقيقي، يمكنك حفظ هذا في قاعدة البيانات
    // await AuditLog.create({
    //   action,
    //   userId: req.user?.id,
    //   userEmail: req.user?.email,
    //   userRole: req.user?.role,
    //   ip,
    //   path: req.path,
    //   method: req.method,
    //   timestamp: new Date()
    // });
    
    next();
  };
};

// Middleware للتحقق من عدم تعديل المستخدم لحسابه الخاص في العمليات الحساسة
export const preventSelfModification = (req, res, next) => {
  const targetUserId = req.params.id;
  const currentUserId = req.user?.id;

  if (targetUserId === currentUserId) {
    return res.status(400).json({
      message: 'Cannot perform this action on your own account',
      code: 'SELF_MODIFICATION_DENIED'
    });
  }

  next();
};

// Middleware للتحقق من وجود المستخدم المستهدف
export const validateTargetUser = async (req, res, next) => {
  try {
    const User = (await import('../models/User.js')).default;
    const targetUserId = req.params.id;
    
    const targetUser = await User.findById(targetUserId).select('-password');
    
    if (!targetUser) {
      return res.status(404).json({
        message: 'Target user not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // إضافة معلومات المستخدم المستهدف إلى الطلب
    req.targetUser = targetUser;
    next();
  } catch (error) {
    console.error('Error validating target user:', error);
    res.status(500).json({
      message: 'Error validating target user',
      code: 'VALIDATION_ERROR'
    });
  }
};

export default {
  superAdminOnly,
  superAdminOrReadOnly,
  logSensitiveAction,
  preventSelfModification,
  validateTargetUser
};
