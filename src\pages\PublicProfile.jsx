import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>ge, <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Award, BookOpen, User } from 'lucide-react';

const API_URI = 'http://localhost:5000'|| 'https://academy-project-backend.onrender.com' ||process.env.REACT_APP_API_URL 

const PublicProfile = () => {
  const { id } = useParams();
  const { user } = useAuth();
  const navigate = useNavigate();

  const [profileData, setProfileData] = useState({
    name: '',
    email: '',
    achievements: [],
    courses: [],
    certificates: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await fetch(
          `${API_URI}/api/public/profile/${id}` ,{
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          }
        );
        if (!response.ok) throw new Error('فشل في تحميل الملف الشخصي');

        const data = await response.json();
        setProfileData(data);
        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };

    fetchProfile();
  }, [id]);

  if (loading) return <div className="text-center py-5"><Spinner animation="border" variant="primary" /></div>;

  if (error) return <Alert variant="danger" className="text-center">{error}</Alert>;

  return (
    <Container className="py-5">
      <Row className="justify-content-center">
        <Col md={8} lg={6}>
          <Card className="shadow-sm border-0">
            <Card.Body>
              <div className="d-flex align-items-center mb-4">
                <User size={48} className="me-3 text-primary" />
                <div>
                  <h4 className="mb-1">{profileData.name}</h4>
                  <p className="text-muted mb-0">{profileData.email}</p>
                </div>
              </div>

              <hr />

              {/* الإنجازات */}
              <h5 className="mb-3"><Award size={18} className="me-2 text-success" />الإنجازات</h5>
              {profileData.achievements.length > 0 ? (
                <ul className="ps-3">
                  {profileData.achievements.map((ach, index) => (
                    <li key={index}>{ach}</li>
                  ))}
                </ul>
              ) : <p className="text-muted">لا توجد إنجازات بعد.</p>}

              {/* الدورات */}
              <h5 className="mt-4 mb-3"><BookOpen size={18} className="me-2 text-info" />الدورات التعليمية</h5>
              {profileData.courses.length > 0 ? (
                <ul className="ps-3">
                  {profileData.courses.map((course, index) => (
                    <li key={index}>{course.title}</li>
                  ))}
                </ul>
              ) : <p className="text-muted">لم يتم التسجيل في أي دورة.</p>}

              {/* الشهادات */}
              <h5 className="mt-4 mb-3"><Award size={18} className="me-2 text-warning" />الشهادات</h5>
              {profileData.certificates.length > 0 ? (
                <ul className="ps-3">
                  {profileData.certificates.map((cert, index) => (
                    <li key={index}>{cert.name} - <small className="text-muted">{cert.date}</small></li>
                  ))}
                </ul>
              ) : <p className="text-muted">لا توجد شهادات حالية.</p>}

            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default PublicProfile;
