import axios from 'axios';
import { API_CONFIG } from '../config/api.js';

const API_URL = `${API_CONFIG.BASE_URL}/api`;

const api = axios.create({
  baseURL: API_URL,
});

// Add token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Handle token expiration
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const authAPI = {
  register: (userData) => api.post('/auth/register', userData),
  login: (userData) => api.post('/auth/login', userData),
  getProfile: () => api.get('/user/profile'), 
  updateProfile: (id, data) => api.put(`/user/profile/${id}`, data),
};

export const coursesAPI = {
  // الوظائف الأساسية
  getAll: (params) => api.get('/courses', { params }),
  getById: (id) => api.get(`/courses/${id}`),
  create: (formData) => api.post('/courses', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  update: (id, formData) => api.put(`/courses/${id}`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),

  // إدارة حالة الدورة
  toggleStatus: (id) => api.patch(`/courses/${id}/toggle-status`),
  softDelete: (id) => api.patch(`/courses/${id}/soft-delete`),
  permanentDelete: (id) => api.delete(`/courses/${id}`),
  duplicate: (id) => api.post(`/courses/${id}/duplicate`),

  // البحث والإحصائيات
  advancedSearch: (params) => api.get('/courses/search/advanced', { params }),
  getStats: () => api.get('/courses/stats/overview'),

  // التسجيل في الدورات
  enroll: (id) => api.post(`/courses/${id}/enroll`),
  checkEnrollmentStatus: (id) => api.get(`/courses/${id}/enrollment-status`),

  // إدارة الدروس
  updateLesson: (courseId, unitIndex, lessonIndex, lessonData) =>
    api.put(`/courses/${courseId}/units/${unitIndex}/lessons/${lessonIndex}`, lessonData),
  addLesson: (courseId, unitIndex, lessonData) =>
    api.post(`/courses/${courseId}/units/${unitIndex}/lessons`, lessonData),
  deleteLesson: (courseId, unitIndex, lessonIndex) =>
    api.delete(`/courses/${courseId}/units/${unitIndex}/lessons/${lessonIndex}`),
};
export const userAPI = {
  getUsers: () => api.get('/user/users'),

  getProfile: () => api.get('/user/profile'),
  // updateProfile: (id, data) => api.put(`/user/profile/${id}`, data),
  getCourses: () => api.get('/user/courses'),

}

export const adminAPI = {
  getUsers: () => api.get('/admin/users'),
  // updateUserRole: (id, role) => api.put(`/admin/users/${id}/role`, { role }),
  // deleteUser: (id) => api.delete(`/admin/users/${id}`),
  getCourses: () => api.get('/admin/courses'),
  getStatistics: () => api.get('/admin/statistics'),
};
// Super Admin API - صلاحيات كاملة للمدير العام
export const supedAdminAPI = {
  // Users management
  getUsers: () => api.get('/super-admin/users'),
  updateUserRole: (userId, role) => api.put(`/super-admin/users/${userId}/role`, { role }),
  deleteUser: (userId) => api.delete(`/super-admin/users/${userId}`),
  createUser: (userData) => api.post('/super-admin/users', userData),

  // System management
  getSystemStats: () => api.get('/super-admin/system/stats'),
  getSystemLogs: () => api.get('/super-admin/system/logs'),
  getStatistics: () => api.get('/super-admin/statistics'),

  // Advanced analytics
  getAdvancedAnalytics: () => api.get('/super-admin/analytics'),

  // Permissions management
  getAllPermissions: () => api.get('/super-admin/permissions'),
  updatePermissions: (userId, permissions) => api.put(`/super-admin/permissions/${userId}`, { permissions }),

  // Admin management
  getAdmins: () => api.get('/super-admin/admins'),
  promoteToAdmin: (userId) => api.put(`/super-admin/admins/${userId}/promote`),
  demoteAdmin: (userId) => api.put(`/super-admin/admins/${userId}/demote`),

  // Course management (full control)
  getAllCourses: () => api.get('/super-admin/courses'),
  forceDeleteCourse: (courseId) => api.delete(`/super-admin/courses/${courseId}/force`),
  toggleCourseStatus: (courseId, status) => api.put(`/super-admin/courses/${courseId}/status`, { isActive: status })
};

export const chatbotAPI = {
  sendMessage: (message) => api.post('/chatbot', { message }),
};

export default api;