import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { coursesAPI } from '../services/api';
import { Card, Button, Alert, Accordion, ProgressBar, Badge } from 'react-bootstrap';
import { useAuth } from '../context/AuthContext';

// واجهة أولية للتعامل مع تقدم الطالب
const userProgressAPI = {
  // userProgressAPI
get: async (userId, courseId) => {
  const res = await fetch(`http://localhost:5000/api/user-progress/${userId}/${courseId}`, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token")}`
    }
  });
  return res.json();
},

update: async (userId, courseId, data) => {
  const res = await fetch(`http://localhost:5000/api/user-progress/${userId}/${courseId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem("token")}`
    },
    body: JSON.stringify(data)
  });
  return res.json();
}
};

const StudentCourse = () => {
  const { id } = useParams();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [course, setCourse] = useState(null);
  const [progress, setProgress] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeUnit, setActiveUnit] = useState(0);
  const [activeLesson, setActiveLesson] = useState(0);
  const [showQuiz, setShowQuiz] = useState(false);
  const [quizResult, setQuizResult] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // جلب بيانات الدورة
        const res = await coursesAPI.getById(id);
        setCourse(res.data);
        console.log(res.data);

        // التحقق من التسجيل في الدورة أولاً
        if (user) {
          try {
            const enrollmentStatus = await coursesAPI.checkEnrollmentStatus(id);
            if (!enrollmentStatus.data.isEnrolled) {
              setError('يجب التسجيل في الدورة أولاً للوصول إلى المحتوى');
              navigate(`/courses/${id}`);
              return;
            }

            // جلب تقدم الطالب من الباكيند
            const prog = await userProgressAPI.get(user._id, res.data._id);
            if (prog && prog.userId === user._id && prog.courseId === res.data._id) {
              setProgress(prog);
              setActiveUnit(prog.currentUnitIndex || 0);
              setActiveLesson(prog.completedLessons ? prog.completedLessons.length : 0);
              setShowQuiz(prog.completedLessons && prog.completedLessons.length >= res.data.units[prog.currentUnitIndex || 0].lessons.length);
            } else {
              // إنشاء تقدم أولي إذا لم يكن موجوداً
              const initialProgress = {
                currentUnitIndex: 0,
                completedLessons: [],
                passedQuizzes: []
              };
              setProgress(initialProgress);
              setActiveUnit(0);
              setActiveLesson(0);
            }
          } catch (enrollmentErr) {
            console.error('Error checking enrollment:', enrollmentErr);
            setError('خطأ في التحقق من التسجيل في الدورة');
          }
        }
      } catch (err) {
        setError('تعذر تحميل بيانات الدورة');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
    // eslint-disable-next-line
  }, [id, user, navigate]);

  // تحديث التقدم عند إكمال درس
  const handleLessonComplete = async () => {
    if (activeLesson < course.units[activeUnit].lessons.length - 1) {
      setActiveLesson(activeLesson + 1);
      // تحديث التقدم في الباكيند
      await userProgressAPI.update(user._id, course._id, {
        currentUnitIndex: activeUnit,
        completedLessons: [...(progress?.completedLessons || []), activeLesson],
        passedQuizzes: progress?.passedQuizzes || []
      });
    } else {
      setShowQuiz(true);
    }
  };

  // تحديث التقدم عند اجتياز الاختبار
  const handleQuizSubmit = async (result) => {
    setQuizResult(result);
    if (result.passed) {
      await userProgressAPI.update(user._id, course._id, {
        currentUnitIndex: activeUnit + 1,
        completedLessons: [],
        passedQuizzes: [...(progress?.passedQuizzes || []), activeUnit]
      });
    }
  };

  const handleNextUnit = () => {
    setActiveUnit(activeUnit + 1);
    setActiveLesson(0);
    setShowQuiz(false);
    setQuizResult(null);
  };

  if (loading) {
    return <div className="text-center py-5"><div className="spinner-border" role="status"></div></div>;
  }
  if (error || !course) {
    return <Alert variant="danger" className="my-5">{error || 'الدورة غير متاحة'}</Alert>;
  }

  const currentUnit = course.units[activeUnit];
  const currentLesson = currentUnit.lessons[activeLesson];

  return (
    <div className="container py-4" dir="rtl">
      <h2 className="fw-bold mb-4">{course.title}</h2>
      <ProgressBar now={((activeUnit) / course.units.length) * 100} label={`الوحدة ${activeUnit + 1} من ${course.units.length}`} className="mb-4" />
      <Accordion defaultActiveKey={activeUnit} alwaysOpen>
        {course.units.map((unit, uidx) => (
          <Accordion.Item eventKey={uidx} key={uidx}>
            <Accordion.Header>
              <span className="fw-bold">الوحدة {uidx + 1}: {unit.title}</span>
              {uidx < activeUnit && <Badge bg="success" className="ms-2">مكتملة</Badge>}
              {uidx === activeUnit && <Badge bg="info" className="ms-2">الحالية</Badge>}
            </Accordion.Header>
            <Accordion.Body>
              {uidx === activeUnit ? (
                <>
                  {!showQuiz ? (
                    <>
                      <Card className="mb-3">
                        <Card.Body>
                          <h5 className="fw-bold mb-3">الدرس {activeLesson + 1}: {currentLesson.type === 'video' ? 'فيديو' : currentLesson.type === 'reading' ? 'قراءة' : 'تطبيق عملي'}</h5>
                          {currentLesson.type === 'video' && (
                            <video src={currentLesson.videoUrl} controls className="w-100 mb-3" style={{ maxHeight: 350 }} />
                          )}
                          {currentLesson.type === 'reading' && (
                            <div className="mb-3"><p>{currentLesson.textContent}</p></div>
                          )}
                          {/* تطبيق عملي يمكن توسيعه لاحقًا */}
                          <Button variant="success" onClick={handleLessonComplete}>
                            {activeLesson < unit.lessons.length - 1 ? 'التالي' : 'بدء اختبار الوحدة'}
                          </Button>
                        </Card.Body>
                      </Card>
                    </>
                  ) : (
                    <Card className="mb-3">
                      <Card.Body>
                        <h5 className="fw-bold mb-3">اختبار نهاية الوحدة</h5>
                        {/* هنا منطق عرض أسئلة الاختبار */}
                        {quizResult ? (
                          <div>
                            <Alert variant={quizResult.passed ? 'success' : 'danger'}>
                              {quizResult.passed ? 'تم اجتياز الاختبار بنجاح! يمكنك الانتقال للوحدة التالية.' : 'لم تجتز الاختبار. يمكنك إعادة المحاولة.'}
                            </Alert>
                            {quizResult.passed && activeUnit < course.units.length - 1 && (
                              <Button variant="primary" onClick={handleNextUnit}>الانتقال للوحدة التالية</Button>
                            )}
                            {!quizResult.passed && (
                              <Button variant="warning" onClick={() => setQuizResult(null)}>إعادة الاختبار</Button>
                            )}
                          </div>
                        ) : (
                          <div>هنا سيتم عرض أسئلة الاختبار (قريبًا)</div>
                        )}
                      </Card.Body>
                    </Card>
                  )}
                </>
              ) : (
                <div className="text-muted">أكمل الوحدات السابقة أولاً للمتابعة.</div>
              )}
            </Accordion.Body>
          </Accordion.Item>
        ))}
      </Accordion>
    </div>
  );
};

export default StudentCourse; 