import React from 'react';
import { Contain<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { useTheme } from '../context/ThemeContext';
import { BookO<PERSON>, Users, Award, ArrowRight } from 'lucide-react';
import { useAuth } from '../context/AuthContext';

const Home = () => {
  const { isDark } = useTheme();
  const {token} = useAuth();

  return (
    <div dir="rtl">
      {/* Hero Section */}
      <div className={`${isDark ? 'bg-info' : 'bg-primary'} text-white py-5`}>
        <Container>
          <Row className="align-items-center">
            <Col lg={6}>
              <h1 className="display-4 fw-bold mb-4">
                اتقن مهارات جديدة مع دورات يقودها خبراء
              </h1>
              <p className="lead mb-4">
                انضم إلى آلاف الطلاب الذين يتعلمون مهارات التكنولوجيا المتطورة
                من المحترفين في الصناعة. ابدأ رحلة التعلم اليوم.
              </p>
              <div className="d-flex gap-3">
                <Button as={Link} to="/courses" variant="light" size="lg">
                  تصفح الدورات
                  <ArrowRight size={20} className="ms-2" />
                </Button>
                {token ? <Button as={Link} to="/register" variant="outline-light" size="lg">
                  انشاء حساب
                  <ArrowRight size={20} className="ms-2" />
                </Button> : <Button as={Link} to="/courses" variant="outline-light" size="lg">
                  تصفح الدورات
                  <ArrowRight size={20} className="ms-2" />
                </Button>}  
              </div>
            </Col>
            <Col lg={6} className="text-center">
              <div className="bg-white bg-opacity-10 rounded-3 p-5">
                <BookOpen size={80} className="mb-3" />
                <h3>ابدأ التعلم اليوم</h3>
                <p>احصل على محتوى متميز وأدوات تعلم تفاعلية</p>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      {/* Features Section */}
      <Container className={`py-5 ${isDark ? 'text-light' : ''}`}>
        <Row className="text-center mb-5">
          <Col>
            <h2 className="fw-bold mb-3">لماذا تختار منصتنا؟</h2>
            <p className="text-muted">كل ما تحتاجه لتطوير مسيرتك المهنية ومهاراتك</p>
          </Col>
        </Row>
        
        <Row className="g-4">
          <Col md={4}>
            <Card className={`border-0 shadow-sm h-100 text-center ${isDark ? 'bg-dark text-light' : 'bg-white'}`}>
              <Card.Body className="p-4">
                <BookOpen size={48} className={`${isDark ? 'text-info' : 'text-primary'} mb-3`} />
                <Card.Title>محتوى يقوده خبراء</Card.Title>
                <Card.Text className="text-muted">
                  تعلم من المحترفين في الصناعة الذين يتمتعون بخبرة عملية
                  وخبرة مثبتة.
                </Card.Text>
              </Card.Body>
            </Card>
          </Col>
          <Col md={4}>
            <Card className={`border-0 shadow-sm h-100 text-center ${isDark ? 'bg-dark text-light' : 'bg-white'}`}>
              <Card.Body className="p-4">
                <Users size={48} className="text-success mb-3" />
                <Card.Title>تعلم تفاعلي</Card.Title>
                <Card.Text className="text-muted">
                  تفاعل مع روبوت الدردشة المدعوم بالذكاء الاصطناعي للحصول على مساعدة فورية
                  ودعم تعلم مخصص.
                </Card.Text>
              </Card.Body>
            </Card>
          </Col>
          <Col md={4}>
            <Card className={`border-0 shadow-sm h-100 text-center ${isDark ? 'bg-dark text-light' : 'bg-white'}`}>
              <Card.Body className="p-4">
                <Award size={48} className="text-warning mb-3" />
                <Card.Title>نمو مهني</Card.Title>
                <Card.Text className="text-muted">
                  اكتسب المهارات التي تحتاجها لتطوير مسيرتك المهنية مع
                  دورات ذات صلة بالصناعة.
                </Card.Text>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>

      {/* CTA Section */}
      <div className={`${isDark ? 'bg-secondary' : 'bg-light'} py-5`}>
        <Container>
          <Row className="text-center">
            <Col lg={8} className="mx-auto">
              <h2 className="fw-bold mb-3">مستعد لبدء التعلم؟</h2>
              <p className="lead text-muted mb-4">
                انضم إلى مجتمع المتعلمين واتخذ الخطوة التالية في مسيرتك المهنية.
              </p>
              <Button as={Link} to={token ?   "/login" : "/courses"} variant="primary" size="lg">
                {token ?   "تسجيل الدخول" : "تصفح الدورات"}
                <ArrowRight size={20} className="ms-2" />
              </Button>
            </Col>
          </Row>
        </Container>
      </div>
    </div>
  );
};

export default Home;