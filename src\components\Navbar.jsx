import React, { useState, useEffect } from 'react';
import { Navbar, Nav, Container, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import { GraduationCap, User, LogOut, Settings, Sun, Moon } from 'lucide-react';
import { use } from 'react';

const AppNavbar = () => {
  const [showSettings, setShowSettings] = useState(false);
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const [logoutTimer, setLogoutTimer] = useState(5); // عداد التأكيد
  // استخدم فقط القيم من AuthContext
  const { user, isAuthenticated, loading, logout , isAdmin, isSuperAdmin} = useAuth();
  const { isDark, toggleTheme } = useTheme();
  const navigate = useNavigate();

  // منطق العداد التنازلي
  useEffect(() => {
    let timer;
    if (showLogoutConfirm && logoutTimer > 0) {
      timer = setTimeout(() => setLogoutTimer(logoutTimer - 1), 1000);
    }
    if (!showLogoutConfirm) {
      setLogoutTimer(5); // إعادة العداد عند إغلاق النافذة
    }
    return () => clearTimeout(timer);
  }, [showLogoutConfirm, logoutTimer]);

  const handleLogout = () => {
    logout();
    setShowSettings(false); // إغلاق نافذة الإعدادات بعد تسجيل الخروج
    setShowLogoutConfirm(false); // إغلاق نافذة التأكيد
    setLogoutTimer(5); // إعادة العداد
    navigate('/');
  };

  // ✅ إظهار شريط تحميل أثناء تحقق المصادقة
  if (loading) {
    return (
      <Navbar bg={isDark ? "dark" : "light"} variant={isDark ? "dark" : "light"} expand="lg" className="shadow-sm border-bottom" dir="rtl">
        <Container className="justify-content-center py-2">
          <Spinner animation="border" variant="primary" size="sm" />
          <div className="ms-2">جارٍ التحقق من حسابك...</div>
        </Container>
      </Navbar>
    );
  }

  return (
    <Navbar bg={isDark ? "dark" : "white"} variant={isDark ? "dark" : "light"} expand="lg" className="shadow-sm border-bottom" dir="rtl">
      <Container>
        <Navbar.Brand as={Link} to="/" className={`fw-bold ${isDark ? 'text-info' : 'text-primary'}`}>
          <GraduationCap className="me-2" size={24} />
          منصة التعليم
        </Navbar.Brand>

        <Navbar.Toggle aria-controls="basic-navbar-nav" />

        <Navbar.Collapse id="basic-navbar-nav">
          <Nav className="me-auto">
            <Nav.Link as={Link} to="/courses">الدورات</Nav.Link>
          </Nav>

              {isAdmin && (
          <Nav>
            <Nav.Link as={Link} to="/admin">
              <Settings size={16} className="me-1" />
              لوحة التحكم (مدير)
            </Nav.Link>
          </Nav>
        )}

        {isSuperAdmin && (
          <Nav>
            <Nav.Link as={Link} to="/admin">
              <Settings size={16} className="me-1" />
              لوحة التحكم (مدير عام)
            </Nav.Link>
          </Nav>
        )}

          

        

          <Nav>
            <Nav.Link as={Link} to="/honor-board">
              <GraduationCap size={16} className="me-1" />
              لائحة الشرف
            </Nav.Link>
          </Nav>

          <Nav className="ms-auto d-flex align-items-center gap-2">
            <Button
              variant="outline-secondary"
              size="sm"
              onClick={toggleTheme}
              className="d-flex align-items-center"
            >
              {isDark ? <Sun size={16} /> : <Moon size={16} />}
            </Button>

            {isAuthenticated && user ? (
              <>
                <span className="me-2 d-flex align-items-center">
                  <User size={16} className="me-1" />
                  {user?.name || "المستخدم"}
                </span>

                <Button
                  variant="outline-primary"
                  size="sm"
                  className="d-flex align-items-center"
                  onClick={() => setShowSettings(true)}
                >
                  <Settings size={16} className="me-1" />
                  الإعدادات
                </Button>
              </>
            ) : (
              <>
                <Nav.Link as={Link} to="/login">تسجيل الدخول</Nav.Link>
                <Nav.Link as={Link} to="/register">
                  <Button variant={isDark ? "info" : "primary"} size="sm">إنشاء حساب</Button>
                </Nav.Link>
              </>
            )}
          </Nav>
        </Navbar.Collapse>
      </Container>

      {/* نافذة الإعدادات */}
      <Modal show={showSettings} onHide={() => setShowSettings(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>الإعدادات</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="d-grid gap-3">
            <Button
              variant="outline-dark"
              onClick={() => {
                navigate('/profile');
                setShowSettings(false);
              }}
              className="d-flex align-items-center justify-content-center"
            >
              <User size={16} className="me-2" />
              الملف الشخصي
            </Button>

            <Button
              variant="outline-danger"
              onClick={() => setShowLogoutConfirm(true)}
              className="d-flex align-items-center justify-content-center"
            >
              <LogOut size={16} className="me-2" />
              تسجيل الخروج
            </Button>

            {showLogoutConfirm && (
              <Alert variant="warning" className="mt-3">
                <p className="mb-2">هل أنت متأكد من تسجيل الخروج؟</p>
                <div className="d-flex justify-content-end gap-2">
                  <Button size="sm" variant="secondary" onClick={() => setShowLogoutConfirm(false)}>
                    إلغاء
                  </Button>
                  <Button size="sm" variant="danger" onClick={handleLogout} disabled={logoutTimer > 0}>
                    {logoutTimer > 0 ? `انتظر ${logoutTimer} ثواني...` : 'نعم، تسجيل الخروج'}
                  </Button>
                </div>
              </Alert>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowSettings(false)}>إغلاق</Button>
        </Modal.Footer>
      </Modal>
    </Navbar>
  );
};

export default AppNavbar;
